/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package connector

import (
	"testing"
)

func TestConnectorImpl_List(t *testing.T) {
	// svc := NewService()
	// connectors, err := svc.List(context.Background())
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// jsonString, err := json.Marshal(connectors)
	// assert.NoError(t, err)

	// t.Log("list", string(jsonString))

	// assert.Equal(t, 3, len(connectors))
}

func TestConnectorImpl_Get(t *testing.T) {
	// svc := NewService()
	// connectors, err := svc.GetByIDs(context.Background(), []int64{999})
	// assert.NoError(t, err)

	// assert.Equal(t, 1, len(connectors))
	// assert.Equal(t, int64(999), connectors[0].ID)
}
