// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/datacopy/internal/dal/model"
)

func newDataCopyTask(db *gorm.DB, opts ...gen.DOOption) dataCopyTask {
	_dataCopyTask := dataCopyTask{}

	_dataCopyTask.dataCopyTaskDo.UseDB(db, opts...)
	_dataCopyTask.dataCopyTaskDo.UseModel(&model.DataCopyTask{})

	tableName := _dataCopyTask.dataCopyTaskDo.TableName()
	_dataCopyTask.ALL = field.NewAsterisk(tableName)
	_dataCopyTask.MasterTaskID = field.NewString(tableName, "master_task_id")
	_dataCopyTask.OriginDataID = field.NewInt64(tableName, "origin_data_id")
	_dataCopyTask.TargetDataID = field.NewInt64(tableName, "target_data_id")
	_dataCopyTask.OriginSpaceID = field.NewInt64(tableName, "origin_space_id")
	_dataCopyTask.TargetSpaceID = field.NewInt64(tableName, "target_space_id")
	_dataCopyTask.OriginUserID = field.NewInt64(tableName, "origin_user_id")
	_dataCopyTask.TargetUserID = field.NewInt64(tableName, "target_user_id")
	_dataCopyTask.OriginAppID = field.NewInt64(tableName, "origin_app_id")
	_dataCopyTask.TargetAppID = field.NewInt64(tableName, "target_app_id")
	_dataCopyTask.DataType = field.NewInt32(tableName, "data_type")
	_dataCopyTask.ExtInfo = field.NewString(tableName, "ext_info")
	_dataCopyTask.StartTime = field.NewInt64(tableName, "start_time")
	_dataCopyTask.FinishTime = field.NewInt64(tableName, "finish_time")
	_dataCopyTask.Status = field.NewInt32(tableName, "status")
	_dataCopyTask.ErrorMsg = field.NewString(tableName, "error_msg")
	_dataCopyTask.ID = field.NewInt64(tableName, "id")

	_dataCopyTask.fillFieldMap()

	return _dataCopyTask
}

// dataCopyTask data方向复制任务记录表
type dataCopyTask struct {
	dataCopyTaskDo

	ALL           field.Asterisk
	MasterTaskID  field.String // 复制任务ID
	OriginDataID  field.Int64  // 源id
	TargetDataID  field.Int64  // 目标id
	OriginSpaceID field.Int64  // 源团队空间
	TargetSpaceID field.Int64  // 目标团队空间
	OriginUserID  field.Int64  // 源用户ID
	TargetUserID  field.Int64  // 目标用户ID
	OriginAppID   field.Int64  // 源AppID
	TargetAppID   field.Int64  // 目标AppID
	DataType      field.Int32  // 数据类型 1:knowledge, 2:database
	ExtInfo       field.String // 存储额外信息
	StartTime     field.Int64  // 任务开始时间
	FinishTime    field.Int64  // 任务结束时间
	Status        field.Int32  // 1:创建 2:执行中 3:成功 4:失败
	ErrorMsg      field.String // 错误信息
	ID            field.Int64  // ID

	fieldMap map[string]field.Expr
}

func (d dataCopyTask) Table(newTableName string) *dataCopyTask {
	d.dataCopyTaskDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d dataCopyTask) As(alias string) *dataCopyTask {
	d.dataCopyTaskDo.DO = *(d.dataCopyTaskDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *dataCopyTask) updateTableName(table string) *dataCopyTask {
	d.ALL = field.NewAsterisk(table)
	d.MasterTaskID = field.NewString(table, "master_task_id")
	d.OriginDataID = field.NewInt64(table, "origin_data_id")
	d.TargetDataID = field.NewInt64(table, "target_data_id")
	d.OriginSpaceID = field.NewInt64(table, "origin_space_id")
	d.TargetSpaceID = field.NewInt64(table, "target_space_id")
	d.OriginUserID = field.NewInt64(table, "origin_user_id")
	d.TargetUserID = field.NewInt64(table, "target_user_id")
	d.OriginAppID = field.NewInt64(table, "origin_app_id")
	d.TargetAppID = field.NewInt64(table, "target_app_id")
	d.DataType = field.NewInt32(table, "data_type")
	d.ExtInfo = field.NewString(table, "ext_info")
	d.StartTime = field.NewInt64(table, "start_time")
	d.FinishTime = field.NewInt64(table, "finish_time")
	d.Status = field.NewInt32(table, "status")
	d.ErrorMsg = field.NewString(table, "error_msg")
	d.ID = field.NewInt64(table, "id")

	d.fillFieldMap()

	return d
}

func (d *dataCopyTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *dataCopyTask) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 16)
	d.fieldMap["master_task_id"] = d.MasterTaskID
	d.fieldMap["origin_data_id"] = d.OriginDataID
	d.fieldMap["target_data_id"] = d.TargetDataID
	d.fieldMap["origin_space_id"] = d.OriginSpaceID
	d.fieldMap["target_space_id"] = d.TargetSpaceID
	d.fieldMap["origin_user_id"] = d.OriginUserID
	d.fieldMap["target_user_id"] = d.TargetUserID
	d.fieldMap["origin_app_id"] = d.OriginAppID
	d.fieldMap["target_app_id"] = d.TargetAppID
	d.fieldMap["data_type"] = d.DataType
	d.fieldMap["ext_info"] = d.ExtInfo
	d.fieldMap["start_time"] = d.StartTime
	d.fieldMap["finish_time"] = d.FinishTime
	d.fieldMap["status"] = d.Status
	d.fieldMap["error_msg"] = d.ErrorMsg
	d.fieldMap["id"] = d.ID
}

func (d dataCopyTask) clone(db *gorm.DB) dataCopyTask {
	d.dataCopyTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d dataCopyTask) replaceDB(db *gorm.DB) dataCopyTask {
	d.dataCopyTaskDo.ReplaceDB(db)
	return d
}

type dataCopyTaskDo struct{ gen.DO }

type IDataCopyTaskDo interface {
	gen.SubQuery
	Debug() IDataCopyTaskDo
	WithContext(ctx context.Context) IDataCopyTaskDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDataCopyTaskDo
	WriteDB() IDataCopyTaskDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDataCopyTaskDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDataCopyTaskDo
	Not(conds ...gen.Condition) IDataCopyTaskDo
	Or(conds ...gen.Condition) IDataCopyTaskDo
	Select(conds ...field.Expr) IDataCopyTaskDo
	Where(conds ...gen.Condition) IDataCopyTaskDo
	Order(conds ...field.Expr) IDataCopyTaskDo
	Distinct(cols ...field.Expr) IDataCopyTaskDo
	Omit(cols ...field.Expr) IDataCopyTaskDo
	Join(table schema.Tabler, on ...field.Expr) IDataCopyTaskDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDataCopyTaskDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDataCopyTaskDo
	Group(cols ...field.Expr) IDataCopyTaskDo
	Having(conds ...gen.Condition) IDataCopyTaskDo
	Limit(limit int) IDataCopyTaskDo
	Offset(offset int) IDataCopyTaskDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDataCopyTaskDo
	Unscoped() IDataCopyTaskDo
	Create(values ...*model.DataCopyTask) error
	CreateInBatches(values []*model.DataCopyTask, batchSize int) error
	Save(values ...*model.DataCopyTask) error
	First() (*model.DataCopyTask, error)
	Take() (*model.DataCopyTask, error)
	Last() (*model.DataCopyTask, error)
	Find() ([]*model.DataCopyTask, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DataCopyTask, err error)
	FindInBatches(result *[]*model.DataCopyTask, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.DataCopyTask) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDataCopyTaskDo
	Assign(attrs ...field.AssignExpr) IDataCopyTaskDo
	Joins(fields ...field.RelationField) IDataCopyTaskDo
	Preload(fields ...field.RelationField) IDataCopyTaskDo
	FirstOrInit() (*model.DataCopyTask, error)
	FirstOrCreate() (*model.DataCopyTask, error)
	FindByPage(offset int, limit int) (result []*model.DataCopyTask, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDataCopyTaskDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d dataCopyTaskDo) Debug() IDataCopyTaskDo {
	return d.withDO(d.DO.Debug())
}

func (d dataCopyTaskDo) WithContext(ctx context.Context) IDataCopyTaskDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d dataCopyTaskDo) ReadDB() IDataCopyTaskDo {
	return d.Clauses(dbresolver.Read)
}

func (d dataCopyTaskDo) WriteDB() IDataCopyTaskDo {
	return d.Clauses(dbresolver.Write)
}

func (d dataCopyTaskDo) Session(config *gorm.Session) IDataCopyTaskDo {
	return d.withDO(d.DO.Session(config))
}

func (d dataCopyTaskDo) Clauses(conds ...clause.Expression) IDataCopyTaskDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d dataCopyTaskDo) Returning(value interface{}, columns ...string) IDataCopyTaskDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d dataCopyTaskDo) Not(conds ...gen.Condition) IDataCopyTaskDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d dataCopyTaskDo) Or(conds ...gen.Condition) IDataCopyTaskDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d dataCopyTaskDo) Select(conds ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d dataCopyTaskDo) Where(conds ...gen.Condition) IDataCopyTaskDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d dataCopyTaskDo) Order(conds ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d dataCopyTaskDo) Distinct(cols ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d dataCopyTaskDo) Omit(cols ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d dataCopyTaskDo) Join(table schema.Tabler, on ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d dataCopyTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d dataCopyTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d dataCopyTaskDo) Group(cols ...field.Expr) IDataCopyTaskDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d dataCopyTaskDo) Having(conds ...gen.Condition) IDataCopyTaskDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d dataCopyTaskDo) Limit(limit int) IDataCopyTaskDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d dataCopyTaskDo) Offset(offset int) IDataCopyTaskDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d dataCopyTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDataCopyTaskDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d dataCopyTaskDo) Unscoped() IDataCopyTaskDo {
	return d.withDO(d.DO.Unscoped())
}

func (d dataCopyTaskDo) Create(values ...*model.DataCopyTask) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d dataCopyTaskDo) CreateInBatches(values []*model.DataCopyTask, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d dataCopyTaskDo) Save(values ...*model.DataCopyTask) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d dataCopyTaskDo) First() (*model.DataCopyTask, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DataCopyTask), nil
	}
}

func (d dataCopyTaskDo) Take() (*model.DataCopyTask, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DataCopyTask), nil
	}
}

func (d dataCopyTaskDo) Last() (*model.DataCopyTask, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DataCopyTask), nil
	}
}

func (d dataCopyTaskDo) Find() ([]*model.DataCopyTask, error) {
	result, err := d.DO.Find()
	return result.([]*model.DataCopyTask), err
}

func (d dataCopyTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DataCopyTask, err error) {
	buf := make([]*model.DataCopyTask, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d dataCopyTaskDo) FindInBatches(result *[]*model.DataCopyTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d dataCopyTaskDo) Attrs(attrs ...field.AssignExpr) IDataCopyTaskDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d dataCopyTaskDo) Assign(attrs ...field.AssignExpr) IDataCopyTaskDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d dataCopyTaskDo) Joins(fields ...field.RelationField) IDataCopyTaskDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d dataCopyTaskDo) Preload(fields ...field.RelationField) IDataCopyTaskDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d dataCopyTaskDo) FirstOrInit() (*model.DataCopyTask, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DataCopyTask), nil
	}
}

func (d dataCopyTaskDo) FirstOrCreate() (*model.DataCopyTask, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DataCopyTask), nil
	}
}

func (d dataCopyTaskDo) FindByPage(offset int, limit int) (result []*model.DataCopyTask, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d dataCopyTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d dataCopyTaskDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d dataCopyTaskDo) Delete(models ...*model.DataCopyTask) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *dataCopyTaskDo) withDO(do gen.Dao) *dataCopyTaskDo {
	d.DO = *do.(*gen.DO)
	return d
}
