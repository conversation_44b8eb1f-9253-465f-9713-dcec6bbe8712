// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "github.com/coze-dev/coze-studio/backend/api/model/crossdomain/agentrun"

const TableNameRunRecord = "run_record"

// RunRecord 执行记录表
type RunRecord struct {
	ID             int64           `gorm:"column:id;primaryKey;comment:主键ID" json:"id"`                                                                                                   // 主键ID
	ConversationID int64           `gorm:"column:conversation_id;not null;comment:会话 ID" json:"conversation_id"`                                                                          // 会话 ID
	SectionID      int64           `gorm:"column:section_id;not null;comment:section ID" json:"section_id"`                                                                               // section ID
	AgentID        int64           `gorm:"column:agent_id;not null;comment:agent_id" json:"agent_id"`                                                                                     // agent_id
	UserID         string          `gorm:"column:user_id;not null;comment:user id" json:"user_id"`                                                                                        // user id
	Source         int32           `gorm:"column:source;not null;comment:执行来源 0 API," json:"source"`                                                                                      // 执行来源 0 API,
	Status         string          `gorm:"column:status;not null;comment:状态,0 Unknown, 1-Created,2-InProgress,3-Completed,4-Failed,5-Expired,6-Cancelled,7-RequiresAction" json:"status"` // 状态,0 Unknown, 1-Created,2-InProgress,3-Completed,4-Failed,5-Expired,6-Cancelled,7-RequiresAction
	CreatorID      int64           `gorm:"column:creator_id;not null;comment:创建者标识" json:"creator_id"`                                                                                    // 创建者标识
	CreatedAt      int64           `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"`                                                                // 创建时间
	UpdatedAt      int64           `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"`                                                                // 更新时间
	FailedAt       int64           `gorm:"column:failed_at;not null;comment:失败时间" json:"failed_at"`                                                                                       // 失败时间
	LastError      string          `gorm:"column:last_error;comment:error message" json:"last_error"`                                                                                     // error message
	CompletedAt    int64           `gorm:"column:completed_at;not null;comment:结束时间" json:"completed_at"`                                                                                 // 结束时间
	ChatRequest    string          `gorm:"column:chat_request;comment:保存原始请求的部分字段" json:"chat_request"`                                                                                   // 保存原始请求的部分字段
	Ext            string          `gorm:"column:ext;comment:扩展字段" json:"ext"`                                                                                                            // 扩展字段
	Usage          *agentrun.Usage `gorm:"column:usage;comment:usage;serializer:json" json:"usage"`                                                                                       // usage
}

// TableName RunRecord's table name
func (*RunRecord) TableName() string {
	return TableNameRunRecord
}
