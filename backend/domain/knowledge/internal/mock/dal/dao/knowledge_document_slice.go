// Code generated by MockGen. DO NOT EDIT.
// Source: knowledge_document_slice.go
//
// Generated by this command:
//
//	mockgen -destination ../../mock/dal/dao/knowledge_document_slice.go --package dao -source knowledge_document_slice.go
//

// Package dao is a generated GoMock package.
package dao

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"

	"github.com/coze-dev/coze-studio/backend/domain/knowledge/entity"
	model "github.com/coze-dev/coze-studio/backend/domain/knowledge/internal/dal/model"
)

// MockKnowledgeDocumentSliceRepo is a mock of KnowledgeDocumentSliceRepo interface.
type MockKnowledgeDocumentSliceRepo struct {
	ctrl     *gomock.Controller
	recorder *MockKnowledgeDocumentSliceRepoMockRecorder
}

// MockKnowledgeDocumentSliceRepoMockRecorder is the mock recorder for MockKnowledgeDocumentSliceRepo.
type MockKnowledgeDocumentSliceRepoMockRecorder struct {
	mock *MockKnowledgeDocumentSliceRepo
}

// NewMockKnowledgeDocumentSliceRepo creates a new mock instance.
func NewMockKnowledgeDocumentSliceRepo(ctrl *gomock.Controller) *MockKnowledgeDocumentSliceRepo {
	mock := &MockKnowledgeDocumentSliceRepo{ctrl: ctrl}
	mock.recorder = &MockKnowledgeDocumentSliceRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKnowledgeDocumentSliceRepo) EXPECT() *MockKnowledgeDocumentSliceRepoMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) BatchCreate(ctx context.Context, slices []*model.KnowledgeDocumentSlice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, slices)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) BatchCreate(ctx, slices any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).BatchCreate), ctx, slices)
}

// BatchSetStatus mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) BatchSetStatus(ctx context.Context, ids []int64, status int32, reason string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetStatus", ctx, ids, status, reason)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetStatus indicates an expected call of BatchSetStatus.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) BatchSetStatus(ctx, ids, status, reason any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetStatus", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).BatchSetStatus), ctx, ids, status, reason)
}

// Create mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) Create(ctx context.Context, slice *model.KnowledgeDocumentSlice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, slice)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) Create(ctx, slice any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).Create), ctx, slice)
}

// Delete mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) Delete(ctx context.Context, slice *model.KnowledgeDocumentSlice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, slice)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) Delete(ctx, slice any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).Delete), ctx, slice)
}

// DeleteByDocument mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) DeleteByDocument(ctx context.Context, documentID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByDocument", ctx, documentID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByDocument indicates an expected call of DeleteByDocument.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) DeleteByDocument(ctx, documentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByDocument", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).DeleteByDocument), ctx, documentID)
}

// FindSliceByCondition mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) FindSliceByCondition(ctx context.Context, opts *entity.WhereSliceOpt) ([]*model.KnowledgeDocumentSlice, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSliceByCondition", ctx, opts)
	ret0, _ := ret[0].([]*model.KnowledgeDocumentSlice)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// FindSliceByCondition indicates an expected call of FindSliceByCondition.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) FindSliceByCondition(ctx, opts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSliceByCondition", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).FindSliceByCondition), ctx, opts)
}

// GetDocumentSliceIDs mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) GetDocumentSliceIDs(ctx context.Context, docIDs []int64) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDocumentSliceIDs", ctx, docIDs)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentSliceIDs indicates an expected call of GetDocumentSliceIDs.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) GetDocumentSliceIDs(ctx, docIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentSliceIDs", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).GetDocumentSliceIDs), ctx, docIDs)
}

// GetSliceBySequence mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) GetSliceBySequence(ctx context.Context, documentID, sequence int64) ([]*model.KnowledgeDocumentSlice, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSliceBySequence", ctx, documentID, sequence)
	ret0, _ := ret[0].([]*model.KnowledgeDocumentSlice)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSliceBySequence indicates an expected call of GetSliceBySequence.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) GetSliceBySequence(ctx, documentID, sequence any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSliceBySequence", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).GetSliceBySequence), ctx, documentID, sequence)
}

// List mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) List(ctx context.Context, knowledgeID, documentID int64, limit int, cursor *string) ([]*model.KnowledgeDocumentSlice, *string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, knowledgeID, documentID, limit, cursor)
	ret0, _ := ret[0].([]*model.KnowledgeDocumentSlice)
	ret1, _ := ret[1].(*string)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// List indicates an expected call of List.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) List(ctx, knowledgeID, documentID, limit, cursor any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).List), ctx, knowledgeID, documentID, limit, cursor)
}

// ListStatus mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) ListStatus(ctx context.Context, documentID int64, limit int, cursor *string) ([]*model.SliceProgress, *string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStatus", ctx, documentID, limit, cursor)
	ret0, _ := ret[0].([]*model.SliceProgress)
	ret1, _ := ret[1].(*string)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// ListStatus indicates an expected call of ListStatus.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) ListStatus(ctx, documentID, limit, cursor any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStatus", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).ListStatus), ctx, documentID, limit, cursor)
}

// MGetSlices mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) MGetSlices(ctx context.Context, sliceIDs []int64) ([]*model.KnowledgeDocumentSlice, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetSlices", ctx, sliceIDs)
	ret0, _ := ret[0].([]*model.KnowledgeDocumentSlice)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetSlices indicates an expected call of MGetSlices.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) MGetSlices(ctx, sliceIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetSlices", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).MGetSlices), ctx, sliceIDs)
}

// Update mocks base method.
func (m *MockKnowledgeDocumentSliceRepo) Update(ctx context.Context, slice *model.KnowledgeDocumentSlice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, slice)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockKnowledgeDocumentSliceRepoMockRecorder) Update(ctx, slice any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockKnowledgeDocumentSliceRepo)(nil).Update), ctx, slice)
}
