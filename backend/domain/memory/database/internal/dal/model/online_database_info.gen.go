// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"github.com/coze-dev/coze-studio/backend/api/model/crossdomain/database"
	"gorm.io/gorm"
)

const TableNameOnlineDatabaseInfo = "online_database_info"

// OnlineDatabaseInfo online database info
type OnlineDatabaseInfo struct {
	ID                int64                 `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                                                                                                              // ID
	AppID             int64                 `gorm:"column:app_id;comment:App ID" json:"app_id"`                                                                                                                             // App ID
	SpaceID           int64                 `gorm:"column:space_id;not null;comment:Space ID" json:"space_id"`                                                                                                              // Space ID
	RelatedDraftID    int64                 `gorm:"column:related_draft_id;not null;comment:The primary key ID of draft_database_info table" json:"related_draft_id"`                                                       // The primary key ID of draft_database_info table
	IsVisible         int32                 `gorm:"column:is_visible;not null;default:1;comment:Visibility: 0 invisible, 1 visible" json:"is_visible"`                                                                      // Visibility: 0 invisible, 1 visible
	PromptDisabled    int32                 `gorm:"column:prompt_disabled;not null;comment:Support prompt calls: 1 not supported, 0 supported" json:"prompt_disabled"`                                                      // Support prompt calls: 1 not supported, 0 supported
	TableName_        string                `gorm:"column:table_name;not null;comment:Table name" json:"table_name"`                                                                                                        // Table name
	TableDesc         string                `gorm:"column:table_desc;comment:Table description" json:"table_desc"`                                                                                                          // Table description
	TableField        []*database.FieldItem `gorm:"column:table_field;comment:Table field info;serializer:json" json:"table_field"`                                                                                         // Table field info
	CreatorID         int64                 `gorm:"column:creator_id;not null;comment:Creator ID" json:"creator_id"`                                                                                                        // Creator ID
	IconURI           string                `gorm:"column:icon_uri;not null;comment:Icon Uri" json:"icon_uri"`                                                                                                              // Icon Uri
	PhysicalTableName string                `gorm:"column:physical_table_name;comment:The name of the real physical table" json:"physical_table_name"`                                                                      // The name of the real physical table
	RwMode            int64                 `gorm:"column:rw_mode;not null;default:1;comment:Read and write permission modes: 1. Limited read and write mode 2. Read-only mode 3. Full read and write mode" json:"rw_mode"` // Read and write permission modes: 1. Limited read and write mode 2. Read-only mode 3. Full read and write mode
	CreatedAt         int64                 `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"`                                                                  // Create Time in Milliseconds
	UpdatedAt         int64                 `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"`                                                                  // Update Time in Milliseconds
	DeletedAt         gorm.DeletedAt        `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                                                                                                // Delete Time
}

// TableName OnlineDatabaseInfo's table name
func (*OnlineDatabaseInfo) TableName() string {
	return TableNameOnlineDatabaseInfo
}
