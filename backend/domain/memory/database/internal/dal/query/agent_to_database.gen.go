// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/memory/database/internal/dal/model"
)

func newAgentToDatabase(db *gorm.DB, opts ...gen.DOOption) agentToDatabase {
	_agentToDatabase := agentToDatabase{}

	_agentToDatabase.agentToDatabaseDo.UseDB(db, opts...)
	_agentToDatabase.agentToDatabaseDo.UseModel(&model.AgentToDatabase{})

	tableName := _agentToDatabase.agentToDatabaseDo.TableName()
	_agentToDatabase.ALL = field.NewAsterisk(tableName)
	_agentToDatabase.ID = field.NewInt64(tableName, "id")
	_agentToDatabase.AgentID = field.NewInt64(tableName, "agent_id")
	_agentToDatabase.DatabaseID = field.NewInt64(tableName, "database_id")
	_agentToDatabase.IsDraft = field.NewBool(tableName, "is_draft")
	_agentToDatabase.PromptDisable = field.NewBool(tableName, "prompt_disable")

	_agentToDatabase.fillFieldMap()

	return _agentToDatabase
}

// agentToDatabase agent_to_database info
type agentToDatabase struct {
	agentToDatabaseDo

	ALL           field.Asterisk
	ID            field.Int64 // ID
	AgentID       field.Int64 // Agent ID
	DatabaseID    field.Int64 // ID of database_info
	IsDraft       field.Bool  // Is draft
	PromptDisable field.Bool  // Support prompt calls: 1 not supported, 0 supported

	fieldMap map[string]field.Expr
}

func (a agentToDatabase) Table(newTableName string) *agentToDatabase {
	a.agentToDatabaseDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a agentToDatabase) As(alias string) *agentToDatabase {
	a.agentToDatabaseDo.DO = *(a.agentToDatabaseDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *agentToDatabase) updateTableName(table string) *agentToDatabase {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AgentID = field.NewInt64(table, "agent_id")
	a.DatabaseID = field.NewInt64(table, "database_id")
	a.IsDraft = field.NewBool(table, "is_draft")
	a.PromptDisable = field.NewBool(table, "prompt_disable")

	a.fillFieldMap()

	return a
}

func (a *agentToDatabase) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *agentToDatabase) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 5)
	a.fieldMap["id"] = a.ID
	a.fieldMap["agent_id"] = a.AgentID
	a.fieldMap["database_id"] = a.DatabaseID
	a.fieldMap["is_draft"] = a.IsDraft
	a.fieldMap["prompt_disable"] = a.PromptDisable
}

func (a agentToDatabase) clone(db *gorm.DB) agentToDatabase {
	a.agentToDatabaseDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a agentToDatabase) replaceDB(db *gorm.DB) agentToDatabase {
	a.agentToDatabaseDo.ReplaceDB(db)
	return a
}

type agentToDatabaseDo struct{ gen.DO }

type IAgentToDatabaseDo interface {
	gen.SubQuery
	Debug() IAgentToDatabaseDo
	WithContext(ctx context.Context) IAgentToDatabaseDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAgentToDatabaseDo
	WriteDB() IAgentToDatabaseDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAgentToDatabaseDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAgentToDatabaseDo
	Not(conds ...gen.Condition) IAgentToDatabaseDo
	Or(conds ...gen.Condition) IAgentToDatabaseDo
	Select(conds ...field.Expr) IAgentToDatabaseDo
	Where(conds ...gen.Condition) IAgentToDatabaseDo
	Order(conds ...field.Expr) IAgentToDatabaseDo
	Distinct(cols ...field.Expr) IAgentToDatabaseDo
	Omit(cols ...field.Expr) IAgentToDatabaseDo
	Join(table schema.Tabler, on ...field.Expr) IAgentToDatabaseDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAgentToDatabaseDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAgentToDatabaseDo
	Group(cols ...field.Expr) IAgentToDatabaseDo
	Having(conds ...gen.Condition) IAgentToDatabaseDo
	Limit(limit int) IAgentToDatabaseDo
	Offset(offset int) IAgentToDatabaseDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAgentToDatabaseDo
	Unscoped() IAgentToDatabaseDo
	Create(values ...*model.AgentToDatabase) error
	CreateInBatches(values []*model.AgentToDatabase, batchSize int) error
	Save(values ...*model.AgentToDatabase) error
	First() (*model.AgentToDatabase, error)
	Take() (*model.AgentToDatabase, error)
	Last() (*model.AgentToDatabase, error)
	Find() ([]*model.AgentToDatabase, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AgentToDatabase, err error)
	FindInBatches(result *[]*model.AgentToDatabase, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AgentToDatabase) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAgentToDatabaseDo
	Assign(attrs ...field.AssignExpr) IAgentToDatabaseDo
	Joins(fields ...field.RelationField) IAgentToDatabaseDo
	Preload(fields ...field.RelationField) IAgentToDatabaseDo
	FirstOrInit() (*model.AgentToDatabase, error)
	FirstOrCreate() (*model.AgentToDatabase, error)
	FindByPage(offset int, limit int) (result []*model.AgentToDatabase, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAgentToDatabaseDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a agentToDatabaseDo) Debug() IAgentToDatabaseDo {
	return a.withDO(a.DO.Debug())
}

func (a agentToDatabaseDo) WithContext(ctx context.Context) IAgentToDatabaseDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a agentToDatabaseDo) ReadDB() IAgentToDatabaseDo {
	return a.Clauses(dbresolver.Read)
}

func (a agentToDatabaseDo) WriteDB() IAgentToDatabaseDo {
	return a.Clauses(dbresolver.Write)
}

func (a agentToDatabaseDo) Session(config *gorm.Session) IAgentToDatabaseDo {
	return a.withDO(a.DO.Session(config))
}

func (a agentToDatabaseDo) Clauses(conds ...clause.Expression) IAgentToDatabaseDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a agentToDatabaseDo) Returning(value interface{}, columns ...string) IAgentToDatabaseDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a agentToDatabaseDo) Not(conds ...gen.Condition) IAgentToDatabaseDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a agentToDatabaseDo) Or(conds ...gen.Condition) IAgentToDatabaseDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a agentToDatabaseDo) Select(conds ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a agentToDatabaseDo) Where(conds ...gen.Condition) IAgentToDatabaseDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a agentToDatabaseDo) Order(conds ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a agentToDatabaseDo) Distinct(cols ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a agentToDatabaseDo) Omit(cols ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a agentToDatabaseDo) Join(table schema.Tabler, on ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a agentToDatabaseDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a agentToDatabaseDo) RightJoin(table schema.Tabler, on ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a agentToDatabaseDo) Group(cols ...field.Expr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a agentToDatabaseDo) Having(conds ...gen.Condition) IAgentToDatabaseDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a agentToDatabaseDo) Limit(limit int) IAgentToDatabaseDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a agentToDatabaseDo) Offset(offset int) IAgentToDatabaseDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a agentToDatabaseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAgentToDatabaseDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a agentToDatabaseDo) Unscoped() IAgentToDatabaseDo {
	return a.withDO(a.DO.Unscoped())
}

func (a agentToDatabaseDo) Create(values ...*model.AgentToDatabase) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a agentToDatabaseDo) CreateInBatches(values []*model.AgentToDatabase, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a agentToDatabaseDo) Save(values ...*model.AgentToDatabase) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a agentToDatabaseDo) First() (*model.AgentToDatabase, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToDatabase), nil
	}
}

func (a agentToDatabaseDo) Take() (*model.AgentToDatabase, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToDatabase), nil
	}
}

func (a agentToDatabaseDo) Last() (*model.AgentToDatabase, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToDatabase), nil
	}
}

func (a agentToDatabaseDo) Find() ([]*model.AgentToDatabase, error) {
	result, err := a.DO.Find()
	return result.([]*model.AgentToDatabase), err
}

func (a agentToDatabaseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AgentToDatabase, err error) {
	buf := make([]*model.AgentToDatabase, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a agentToDatabaseDo) FindInBatches(result *[]*model.AgentToDatabase, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a agentToDatabaseDo) Attrs(attrs ...field.AssignExpr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a agentToDatabaseDo) Assign(attrs ...field.AssignExpr) IAgentToDatabaseDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a agentToDatabaseDo) Joins(fields ...field.RelationField) IAgentToDatabaseDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a agentToDatabaseDo) Preload(fields ...field.RelationField) IAgentToDatabaseDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a agentToDatabaseDo) FirstOrInit() (*model.AgentToDatabase, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToDatabase), nil
	}
}

func (a agentToDatabaseDo) FirstOrCreate() (*model.AgentToDatabase, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AgentToDatabase), nil
	}
}

func (a agentToDatabaseDo) FindByPage(offset int, limit int) (result []*model.AgentToDatabase, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a agentToDatabaseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a agentToDatabaseDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a agentToDatabaseDo) Delete(models ...*model.AgentToDatabase) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *agentToDatabaseDo) withDO(do gen.Dao) *agentToDatabaseDo {
	a.DO = *do.(*gen.DO)
	return a
}
