// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/memory/database/internal/dal/model"
)

func newOnlineDatabaseInfo(db *gorm.DB, opts ...gen.DOOption) onlineDatabaseInfo {
	_onlineDatabaseInfo := onlineDatabaseInfo{}

	_onlineDatabaseInfo.onlineDatabaseInfoDo.UseDB(db, opts...)
	_onlineDatabaseInfo.onlineDatabaseInfoDo.UseModel(&model.OnlineDatabaseInfo{})

	tableName := _onlineDatabaseInfo.onlineDatabaseInfoDo.TableName()
	_onlineDatabaseInfo.ALL = field.NewAsterisk(tableName)
	_onlineDatabaseInfo.ID = field.NewInt64(tableName, "id")
	_onlineDatabaseInfo.AppID = field.NewInt64(tableName, "app_id")
	_onlineDatabaseInfo.SpaceID = field.NewInt64(tableName, "space_id")
	_onlineDatabaseInfo.RelatedDraftID = field.NewInt64(tableName, "related_draft_id")
	_onlineDatabaseInfo.IsVisible = field.NewInt32(tableName, "is_visible")
	_onlineDatabaseInfo.PromptDisabled = field.NewInt32(tableName, "prompt_disabled")
	_onlineDatabaseInfo.TableName_ = field.NewString(tableName, "table_name")
	_onlineDatabaseInfo.TableDesc = field.NewString(tableName, "table_desc")
	_onlineDatabaseInfo.TableField = field.NewField(tableName, "table_field")
	_onlineDatabaseInfo.CreatorID = field.NewInt64(tableName, "creator_id")
	_onlineDatabaseInfo.IconURI = field.NewString(tableName, "icon_uri")
	_onlineDatabaseInfo.PhysicalTableName = field.NewString(tableName, "physical_table_name")
	_onlineDatabaseInfo.RwMode = field.NewInt64(tableName, "rw_mode")
	_onlineDatabaseInfo.CreatedAt = field.NewInt64(tableName, "created_at")
	_onlineDatabaseInfo.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_onlineDatabaseInfo.DeletedAt = field.NewField(tableName, "deleted_at")

	_onlineDatabaseInfo.fillFieldMap()

	return _onlineDatabaseInfo
}

// onlineDatabaseInfo online database info
type onlineDatabaseInfo struct {
	onlineDatabaseInfoDo

	ALL               field.Asterisk
	ID                field.Int64  // ID
	AppID             field.Int64  // App ID
	SpaceID           field.Int64  // Space ID
	RelatedDraftID    field.Int64  // The primary key ID of draft_database_info table
	IsVisible         field.Int32  // Visibility: 0 invisible, 1 visible
	PromptDisabled    field.Int32  // Support prompt calls: 1 not supported, 0 supported
	TableName_        field.String // Table name
	TableDesc         field.String // Table description
	TableField        field.Field  // Table field info
	CreatorID         field.Int64  // Creator ID
	IconURI           field.String // Icon Uri
	PhysicalTableName field.String // The name of the real physical table
	RwMode            field.Int64  // Read and write permission modes: 1. Limited read and write mode 2. Read-only mode 3. Full read and write mode
	CreatedAt         field.Int64  // Create Time in Milliseconds
	UpdatedAt         field.Int64  // Update Time in Milliseconds
	DeletedAt         field.Field  // Delete Time

	fieldMap map[string]field.Expr
}

func (o onlineDatabaseInfo) Table(newTableName string) *onlineDatabaseInfo {
	o.onlineDatabaseInfoDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o onlineDatabaseInfo) As(alias string) *onlineDatabaseInfo {
	o.onlineDatabaseInfoDo.DO = *(o.onlineDatabaseInfoDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *onlineDatabaseInfo) updateTableName(table string) *onlineDatabaseInfo {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewInt64(table, "id")
	o.AppID = field.NewInt64(table, "app_id")
	o.SpaceID = field.NewInt64(table, "space_id")
	o.RelatedDraftID = field.NewInt64(table, "related_draft_id")
	o.IsVisible = field.NewInt32(table, "is_visible")
	o.PromptDisabled = field.NewInt32(table, "prompt_disabled")
	o.TableName_ = field.NewString(table, "table_name")
	o.TableDesc = field.NewString(table, "table_desc")
	o.TableField = field.NewField(table, "table_field")
	o.CreatorID = field.NewInt64(table, "creator_id")
	o.IconURI = field.NewString(table, "icon_uri")
	o.PhysicalTableName = field.NewString(table, "physical_table_name")
	o.RwMode = field.NewInt64(table, "rw_mode")
	o.CreatedAt = field.NewInt64(table, "created_at")
	o.UpdatedAt = field.NewInt64(table, "updated_at")
	o.DeletedAt = field.NewField(table, "deleted_at")

	o.fillFieldMap()

	return o
}

func (o *onlineDatabaseInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *onlineDatabaseInfo) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 16)
	o.fieldMap["id"] = o.ID
	o.fieldMap["app_id"] = o.AppID
	o.fieldMap["space_id"] = o.SpaceID
	o.fieldMap["related_draft_id"] = o.RelatedDraftID
	o.fieldMap["is_visible"] = o.IsVisible
	o.fieldMap["prompt_disabled"] = o.PromptDisabled
	o.fieldMap["table_name"] = o.TableName_
	o.fieldMap["table_desc"] = o.TableDesc
	o.fieldMap["table_field"] = o.TableField
	o.fieldMap["creator_id"] = o.CreatorID
	o.fieldMap["icon_uri"] = o.IconURI
	o.fieldMap["physical_table_name"] = o.PhysicalTableName
	o.fieldMap["rw_mode"] = o.RwMode
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["deleted_at"] = o.DeletedAt
}

func (o onlineDatabaseInfo) clone(db *gorm.DB) onlineDatabaseInfo {
	o.onlineDatabaseInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o onlineDatabaseInfo) replaceDB(db *gorm.DB) onlineDatabaseInfo {
	o.onlineDatabaseInfoDo.ReplaceDB(db)
	return o
}

type onlineDatabaseInfoDo struct{ gen.DO }

type IOnlineDatabaseInfoDo interface {
	gen.SubQuery
	Debug() IOnlineDatabaseInfoDo
	WithContext(ctx context.Context) IOnlineDatabaseInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOnlineDatabaseInfoDo
	WriteDB() IOnlineDatabaseInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOnlineDatabaseInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOnlineDatabaseInfoDo
	Not(conds ...gen.Condition) IOnlineDatabaseInfoDo
	Or(conds ...gen.Condition) IOnlineDatabaseInfoDo
	Select(conds ...field.Expr) IOnlineDatabaseInfoDo
	Where(conds ...gen.Condition) IOnlineDatabaseInfoDo
	Order(conds ...field.Expr) IOnlineDatabaseInfoDo
	Distinct(cols ...field.Expr) IOnlineDatabaseInfoDo
	Omit(cols ...field.Expr) IOnlineDatabaseInfoDo
	Join(table schema.Tabler, on ...field.Expr) IOnlineDatabaseInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOnlineDatabaseInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOnlineDatabaseInfoDo
	Group(cols ...field.Expr) IOnlineDatabaseInfoDo
	Having(conds ...gen.Condition) IOnlineDatabaseInfoDo
	Limit(limit int) IOnlineDatabaseInfoDo
	Offset(offset int) IOnlineDatabaseInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOnlineDatabaseInfoDo
	Unscoped() IOnlineDatabaseInfoDo
	Create(values ...*model.OnlineDatabaseInfo) error
	CreateInBatches(values []*model.OnlineDatabaseInfo, batchSize int) error
	Save(values ...*model.OnlineDatabaseInfo) error
	First() (*model.OnlineDatabaseInfo, error)
	Take() (*model.OnlineDatabaseInfo, error)
	Last() (*model.OnlineDatabaseInfo, error)
	Find() ([]*model.OnlineDatabaseInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OnlineDatabaseInfo, err error)
	FindInBatches(result *[]*model.OnlineDatabaseInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.OnlineDatabaseInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOnlineDatabaseInfoDo
	Assign(attrs ...field.AssignExpr) IOnlineDatabaseInfoDo
	Joins(fields ...field.RelationField) IOnlineDatabaseInfoDo
	Preload(fields ...field.RelationField) IOnlineDatabaseInfoDo
	FirstOrInit() (*model.OnlineDatabaseInfo, error)
	FirstOrCreate() (*model.OnlineDatabaseInfo, error)
	FindByPage(offset int, limit int) (result []*model.OnlineDatabaseInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOnlineDatabaseInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o onlineDatabaseInfoDo) Debug() IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Debug())
}

func (o onlineDatabaseInfoDo) WithContext(ctx context.Context) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o onlineDatabaseInfoDo) ReadDB() IOnlineDatabaseInfoDo {
	return o.Clauses(dbresolver.Read)
}

func (o onlineDatabaseInfoDo) WriteDB() IOnlineDatabaseInfoDo {
	return o.Clauses(dbresolver.Write)
}

func (o onlineDatabaseInfoDo) Session(config *gorm.Session) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Session(config))
}

func (o onlineDatabaseInfoDo) Clauses(conds ...clause.Expression) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o onlineDatabaseInfoDo) Returning(value interface{}, columns ...string) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o onlineDatabaseInfoDo) Not(conds ...gen.Condition) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o onlineDatabaseInfoDo) Or(conds ...gen.Condition) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o onlineDatabaseInfoDo) Select(conds ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o onlineDatabaseInfoDo) Where(conds ...gen.Condition) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o onlineDatabaseInfoDo) Order(conds ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o onlineDatabaseInfoDo) Distinct(cols ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o onlineDatabaseInfoDo) Omit(cols ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o onlineDatabaseInfoDo) Join(table schema.Tabler, on ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o onlineDatabaseInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o onlineDatabaseInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o onlineDatabaseInfoDo) Group(cols ...field.Expr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o onlineDatabaseInfoDo) Having(conds ...gen.Condition) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o onlineDatabaseInfoDo) Limit(limit int) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o onlineDatabaseInfoDo) Offset(offset int) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o onlineDatabaseInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o onlineDatabaseInfoDo) Unscoped() IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Unscoped())
}

func (o onlineDatabaseInfoDo) Create(values ...*model.OnlineDatabaseInfo) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o onlineDatabaseInfoDo) CreateInBatches(values []*model.OnlineDatabaseInfo, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o onlineDatabaseInfoDo) Save(values ...*model.OnlineDatabaseInfo) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o onlineDatabaseInfoDo) First() (*model.OnlineDatabaseInfo, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineDatabaseInfo), nil
	}
}

func (o onlineDatabaseInfoDo) Take() (*model.OnlineDatabaseInfo, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineDatabaseInfo), nil
	}
}

func (o onlineDatabaseInfoDo) Last() (*model.OnlineDatabaseInfo, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineDatabaseInfo), nil
	}
}

func (o onlineDatabaseInfoDo) Find() ([]*model.OnlineDatabaseInfo, error) {
	result, err := o.DO.Find()
	return result.([]*model.OnlineDatabaseInfo), err
}

func (o onlineDatabaseInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OnlineDatabaseInfo, err error) {
	buf := make([]*model.OnlineDatabaseInfo, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o onlineDatabaseInfoDo) FindInBatches(result *[]*model.OnlineDatabaseInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o onlineDatabaseInfoDo) Attrs(attrs ...field.AssignExpr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o onlineDatabaseInfoDo) Assign(attrs ...field.AssignExpr) IOnlineDatabaseInfoDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o onlineDatabaseInfoDo) Joins(fields ...field.RelationField) IOnlineDatabaseInfoDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o onlineDatabaseInfoDo) Preload(fields ...field.RelationField) IOnlineDatabaseInfoDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o onlineDatabaseInfoDo) FirstOrInit() (*model.OnlineDatabaseInfo, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineDatabaseInfo), nil
	}
}

func (o onlineDatabaseInfoDo) FirstOrCreate() (*model.OnlineDatabaseInfo, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.OnlineDatabaseInfo), nil
	}
}

func (o onlineDatabaseInfoDo) FindByPage(offset int, limit int) (result []*model.OnlineDatabaseInfo, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o onlineDatabaseInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o onlineDatabaseInfoDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o onlineDatabaseInfoDo) Delete(models ...*model.OnlineDatabaseInfo) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *onlineDatabaseInfoDo) withDO(do gen.Dao) *onlineDatabaseInfoDo {
	o.DO = *do.(*gen.DO)
	return o
}
