// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/memory/variables/internal/dal/model"
)

func newVariableInstance(db *gorm.DB, opts ...gen.DOOption) variableInstance {
	_variableInstance := variableInstance{}

	_variableInstance.variableInstanceDo.UseDB(db, opts...)
	_variableInstance.variableInstanceDo.UseModel(&model.VariableInstance{})

	tableName := _variableInstance.variableInstanceDo.TableName()
	_variableInstance.ALL = field.NewAsterisk(tableName)
	_variableInstance.ID = field.NewInt64(tableName, "id")
	_variableInstance.BizType = field.NewInt32(tableName, "biz_type")
	_variableInstance.BizID = field.NewString(tableName, "biz_id")
	_variableInstance.Version = field.NewString(tableName, "version")
	_variableInstance.Keyword = field.NewString(tableName, "keyword")
	_variableInstance.Type = field.NewInt32(tableName, "type")
	_variableInstance.Content = field.NewString(tableName, "content")
	_variableInstance.ConnectorUID = field.NewString(tableName, "connector_uid")
	_variableInstance.ConnectorID = field.NewInt64(tableName, "connector_id")
	_variableInstance.CreatedAt = field.NewInt64(tableName, "created_at")
	_variableInstance.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_variableInstance.fillFieldMap()

	return _variableInstance
}

// variableInstance KV Memory
type variableInstance struct {
	variableInstanceDo

	ALL          field.Asterisk
	ID           field.Int64  // 主键ID
	BizType      field.Int32  // 1 for agent，2 for app
	BizID        field.String // 1 for agent_id，2 for app_id
	Version      field.String // agent or project 版本,为空代表草稿态
	Keyword      field.String // 记忆的KEY
	Type         field.Int32  // 记忆类型 1 KV 2 list
	Content      field.String // 记忆内容
	ConnectorUID field.String // 二方用户ID
	ConnectorID  field.Int64  // 二方id, e.g. coze = 10000010
	CreatedAt    field.Int64  // 创建时间
	UpdatedAt    field.Int64  // 更新时间

	fieldMap map[string]field.Expr
}

func (v variableInstance) Table(newTableName string) *variableInstance {
	v.variableInstanceDo.UseTable(newTableName)
	return v.updateTableName(newTableName)
}

func (v variableInstance) As(alias string) *variableInstance {
	v.variableInstanceDo.DO = *(v.variableInstanceDo.As(alias).(*gen.DO))
	return v.updateTableName(alias)
}

func (v *variableInstance) updateTableName(table string) *variableInstance {
	v.ALL = field.NewAsterisk(table)
	v.ID = field.NewInt64(table, "id")
	v.BizType = field.NewInt32(table, "biz_type")
	v.BizID = field.NewString(table, "biz_id")
	v.Version = field.NewString(table, "version")
	v.Keyword = field.NewString(table, "keyword")
	v.Type = field.NewInt32(table, "type")
	v.Content = field.NewString(table, "content")
	v.ConnectorUID = field.NewString(table, "connector_uid")
	v.ConnectorID = field.NewInt64(table, "connector_id")
	v.CreatedAt = field.NewInt64(table, "created_at")
	v.UpdatedAt = field.NewInt64(table, "updated_at")

	v.fillFieldMap()

	return v
}

func (v *variableInstance) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := v.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (v *variableInstance) fillFieldMap() {
	v.fieldMap = make(map[string]field.Expr, 11)
	v.fieldMap["id"] = v.ID
	v.fieldMap["biz_type"] = v.BizType
	v.fieldMap["biz_id"] = v.BizID
	v.fieldMap["version"] = v.Version
	v.fieldMap["keyword"] = v.Keyword
	v.fieldMap["type"] = v.Type
	v.fieldMap["content"] = v.Content
	v.fieldMap["connector_uid"] = v.ConnectorUID
	v.fieldMap["connector_id"] = v.ConnectorID
	v.fieldMap["created_at"] = v.CreatedAt
	v.fieldMap["updated_at"] = v.UpdatedAt
}

func (v variableInstance) clone(db *gorm.DB) variableInstance {
	v.variableInstanceDo.ReplaceConnPool(db.Statement.ConnPool)
	return v
}

func (v variableInstance) replaceDB(db *gorm.DB) variableInstance {
	v.variableInstanceDo.ReplaceDB(db)
	return v
}

type variableInstanceDo struct{ gen.DO }

type IVariableInstanceDo interface {
	gen.SubQuery
	Debug() IVariableInstanceDo
	WithContext(ctx context.Context) IVariableInstanceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IVariableInstanceDo
	WriteDB() IVariableInstanceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IVariableInstanceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IVariableInstanceDo
	Not(conds ...gen.Condition) IVariableInstanceDo
	Or(conds ...gen.Condition) IVariableInstanceDo
	Select(conds ...field.Expr) IVariableInstanceDo
	Where(conds ...gen.Condition) IVariableInstanceDo
	Order(conds ...field.Expr) IVariableInstanceDo
	Distinct(cols ...field.Expr) IVariableInstanceDo
	Omit(cols ...field.Expr) IVariableInstanceDo
	Join(table schema.Tabler, on ...field.Expr) IVariableInstanceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IVariableInstanceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IVariableInstanceDo
	Group(cols ...field.Expr) IVariableInstanceDo
	Having(conds ...gen.Condition) IVariableInstanceDo
	Limit(limit int) IVariableInstanceDo
	Offset(offset int) IVariableInstanceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IVariableInstanceDo
	Unscoped() IVariableInstanceDo
	Create(values ...*model.VariableInstance) error
	CreateInBatches(values []*model.VariableInstance, batchSize int) error
	Save(values ...*model.VariableInstance) error
	First() (*model.VariableInstance, error)
	Take() (*model.VariableInstance, error)
	Last() (*model.VariableInstance, error)
	Find() ([]*model.VariableInstance, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.VariableInstance, err error)
	FindInBatches(result *[]*model.VariableInstance, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.VariableInstance) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IVariableInstanceDo
	Assign(attrs ...field.AssignExpr) IVariableInstanceDo
	Joins(fields ...field.RelationField) IVariableInstanceDo
	Preload(fields ...field.RelationField) IVariableInstanceDo
	FirstOrInit() (*model.VariableInstance, error)
	FirstOrCreate() (*model.VariableInstance, error)
	FindByPage(offset int, limit int) (result []*model.VariableInstance, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IVariableInstanceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (v variableInstanceDo) Debug() IVariableInstanceDo {
	return v.withDO(v.DO.Debug())
}

func (v variableInstanceDo) WithContext(ctx context.Context) IVariableInstanceDo {
	return v.withDO(v.DO.WithContext(ctx))
}

func (v variableInstanceDo) ReadDB() IVariableInstanceDo {
	return v.Clauses(dbresolver.Read)
}

func (v variableInstanceDo) WriteDB() IVariableInstanceDo {
	return v.Clauses(dbresolver.Write)
}

func (v variableInstanceDo) Session(config *gorm.Session) IVariableInstanceDo {
	return v.withDO(v.DO.Session(config))
}

func (v variableInstanceDo) Clauses(conds ...clause.Expression) IVariableInstanceDo {
	return v.withDO(v.DO.Clauses(conds...))
}

func (v variableInstanceDo) Returning(value interface{}, columns ...string) IVariableInstanceDo {
	return v.withDO(v.DO.Returning(value, columns...))
}

func (v variableInstanceDo) Not(conds ...gen.Condition) IVariableInstanceDo {
	return v.withDO(v.DO.Not(conds...))
}

func (v variableInstanceDo) Or(conds ...gen.Condition) IVariableInstanceDo {
	return v.withDO(v.DO.Or(conds...))
}

func (v variableInstanceDo) Select(conds ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.Select(conds...))
}

func (v variableInstanceDo) Where(conds ...gen.Condition) IVariableInstanceDo {
	return v.withDO(v.DO.Where(conds...))
}

func (v variableInstanceDo) Order(conds ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.Order(conds...))
}

func (v variableInstanceDo) Distinct(cols ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.Distinct(cols...))
}

func (v variableInstanceDo) Omit(cols ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.Omit(cols...))
}

func (v variableInstanceDo) Join(table schema.Tabler, on ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.Join(table, on...))
}

func (v variableInstanceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.LeftJoin(table, on...))
}

func (v variableInstanceDo) RightJoin(table schema.Tabler, on ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.RightJoin(table, on...))
}

func (v variableInstanceDo) Group(cols ...field.Expr) IVariableInstanceDo {
	return v.withDO(v.DO.Group(cols...))
}

func (v variableInstanceDo) Having(conds ...gen.Condition) IVariableInstanceDo {
	return v.withDO(v.DO.Having(conds...))
}

func (v variableInstanceDo) Limit(limit int) IVariableInstanceDo {
	return v.withDO(v.DO.Limit(limit))
}

func (v variableInstanceDo) Offset(offset int) IVariableInstanceDo {
	return v.withDO(v.DO.Offset(offset))
}

func (v variableInstanceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IVariableInstanceDo {
	return v.withDO(v.DO.Scopes(funcs...))
}

func (v variableInstanceDo) Unscoped() IVariableInstanceDo {
	return v.withDO(v.DO.Unscoped())
}

func (v variableInstanceDo) Create(values ...*model.VariableInstance) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Create(values)
}

func (v variableInstanceDo) CreateInBatches(values []*model.VariableInstance, batchSize int) error {
	return v.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (v variableInstanceDo) Save(values ...*model.VariableInstance) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Save(values)
}

func (v variableInstanceDo) First() (*model.VariableInstance, error) {
	if result, err := v.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariableInstance), nil
	}
}

func (v variableInstanceDo) Take() (*model.VariableInstance, error) {
	if result, err := v.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariableInstance), nil
	}
}

func (v variableInstanceDo) Last() (*model.VariableInstance, error) {
	if result, err := v.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariableInstance), nil
	}
}

func (v variableInstanceDo) Find() ([]*model.VariableInstance, error) {
	result, err := v.DO.Find()
	return result.([]*model.VariableInstance), err
}

func (v variableInstanceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.VariableInstance, err error) {
	buf := make([]*model.VariableInstance, 0, batchSize)
	err = v.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (v variableInstanceDo) FindInBatches(result *[]*model.VariableInstance, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return v.DO.FindInBatches(result, batchSize, fc)
}

func (v variableInstanceDo) Attrs(attrs ...field.AssignExpr) IVariableInstanceDo {
	return v.withDO(v.DO.Attrs(attrs...))
}

func (v variableInstanceDo) Assign(attrs ...field.AssignExpr) IVariableInstanceDo {
	return v.withDO(v.DO.Assign(attrs...))
}

func (v variableInstanceDo) Joins(fields ...field.RelationField) IVariableInstanceDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Joins(_f))
	}
	return &v
}

func (v variableInstanceDo) Preload(fields ...field.RelationField) IVariableInstanceDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Preload(_f))
	}
	return &v
}

func (v variableInstanceDo) FirstOrInit() (*model.VariableInstance, error) {
	if result, err := v.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariableInstance), nil
	}
}

func (v variableInstanceDo) FirstOrCreate() (*model.VariableInstance, error) {
	if result, err := v.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.VariableInstance), nil
	}
}

func (v variableInstanceDo) FindByPage(offset int, limit int) (result []*model.VariableInstance, count int64, err error) {
	result, err = v.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = v.Offset(-1).Limit(-1).Count()
	return
}

func (v variableInstanceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = v.Count()
	if err != nil {
		return
	}

	err = v.Offset(offset).Limit(limit).Scan(result)
	return
}

func (v variableInstanceDo) Scan(result interface{}) (err error) {
	return v.DO.Scan(result)
}

func (v variableInstanceDo) Delete(models ...*model.VariableInstance) (result gen.ResultInfo, err error) {
	return v.DO.Delete(models)
}

func (v *variableInstanceDo) withDO(do gen.Dao) *variableInstanceDo {
	v.DO = *do.(*gen.DO)
	return v
}
