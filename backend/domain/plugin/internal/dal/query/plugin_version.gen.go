// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/plugin/internal/dal/model"
)

func newPluginVersion(db *gorm.DB, opts ...gen.DOOption) pluginVersion {
	_pluginVersion := pluginVersion{}

	_pluginVersion.pluginVersionDo.UseDB(db, opts...)
	_pluginVersion.pluginVersionDo.UseModel(&model.PluginVersion{})

	tableName := _pluginVersion.pluginVersionDo.TableName()
	_pluginVersion.ALL = field.NewAsterisk(tableName)
	_pluginVersion.ID = field.NewInt64(tableName, "id")
	_pluginVersion.SpaceID = field.NewInt64(tableName, "space_id")
	_pluginVersion.DeveloperID = field.NewInt64(tableName, "developer_id")
	_pluginVersion.PluginID = field.NewInt64(tableName, "plugin_id")
	_pluginVersion.AppID = field.NewInt64(tableName, "app_id")
	_pluginVersion.IconURI = field.NewString(tableName, "icon_uri")
	_pluginVersion.ServerURL = field.NewString(tableName, "server_url")
	_pluginVersion.PluginType = field.NewInt32(tableName, "plugin_type")
	_pluginVersion.Version = field.NewString(tableName, "version")
	_pluginVersion.VersionDesc = field.NewString(tableName, "version_desc")
	_pluginVersion.Manifest = field.NewField(tableName, "manifest")
	_pluginVersion.OpenapiDoc = field.NewField(tableName, "openapi_doc")
	_pluginVersion.CreatedAt = field.NewInt64(tableName, "created_at")
	_pluginVersion.DeletedAt = field.NewField(tableName, "deleted_at")

	_pluginVersion.fillFieldMap()

	return _pluginVersion
}

// pluginVersion Plugin Version
type pluginVersion struct {
	pluginVersionDo

	ALL         field.Asterisk
	ID          field.Int64  // Primary Key ID
	SpaceID     field.Int64  // Space ID
	DeveloperID field.Int64  // Developer ID
	PluginID    field.Int64  // Plugin ID
	AppID       field.Int64  // Application ID
	IconURI     field.String // Icon URI
	ServerURL   field.String // Server URL
	PluginType  field.Int32  // Plugin Type, 1:http, 6:local
	Version     field.String // Plugin Version, e.g. v1.0.0
	VersionDesc field.String // Plugin Version Description
	Manifest    field.Field  // Plugin Manifest
	OpenapiDoc  field.Field  // OpenAPI Document, only stores the root
	CreatedAt   field.Int64  // Create Time in Milliseconds
	DeletedAt   field.Field  // Delete Time

	fieldMap map[string]field.Expr
}

func (p pluginVersion) Table(newTableName string) *pluginVersion {
	p.pluginVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pluginVersion) As(alias string) *pluginVersion {
	p.pluginVersionDo.DO = *(p.pluginVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pluginVersion) updateTableName(table string) *pluginVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.SpaceID = field.NewInt64(table, "space_id")
	p.DeveloperID = field.NewInt64(table, "developer_id")
	p.PluginID = field.NewInt64(table, "plugin_id")
	p.AppID = field.NewInt64(table, "app_id")
	p.IconURI = field.NewString(table, "icon_uri")
	p.ServerURL = field.NewString(table, "server_url")
	p.PluginType = field.NewInt32(table, "plugin_type")
	p.Version = field.NewString(table, "version")
	p.VersionDesc = field.NewString(table, "version_desc")
	p.Manifest = field.NewField(table, "manifest")
	p.OpenapiDoc = field.NewField(table, "openapi_doc")
	p.CreatedAt = field.NewInt64(table, "created_at")
	p.DeletedAt = field.NewField(table, "deleted_at")

	p.fillFieldMap()

	return p
}

func (p *pluginVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pluginVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 14)
	p.fieldMap["id"] = p.ID
	p.fieldMap["space_id"] = p.SpaceID
	p.fieldMap["developer_id"] = p.DeveloperID
	p.fieldMap["plugin_id"] = p.PluginID
	p.fieldMap["app_id"] = p.AppID
	p.fieldMap["icon_uri"] = p.IconURI
	p.fieldMap["server_url"] = p.ServerURL
	p.fieldMap["plugin_type"] = p.PluginType
	p.fieldMap["version"] = p.Version
	p.fieldMap["version_desc"] = p.VersionDesc
	p.fieldMap["manifest"] = p.Manifest
	p.fieldMap["openapi_doc"] = p.OpenapiDoc
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
}

func (p pluginVersion) clone(db *gorm.DB) pluginVersion {
	p.pluginVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pluginVersion) replaceDB(db *gorm.DB) pluginVersion {
	p.pluginVersionDo.ReplaceDB(db)
	return p
}

type pluginVersionDo struct{ gen.DO }

type IPluginVersionDo interface {
	gen.SubQuery
	Debug() IPluginVersionDo
	WithContext(ctx context.Context) IPluginVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPluginVersionDo
	WriteDB() IPluginVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPluginVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPluginVersionDo
	Not(conds ...gen.Condition) IPluginVersionDo
	Or(conds ...gen.Condition) IPluginVersionDo
	Select(conds ...field.Expr) IPluginVersionDo
	Where(conds ...gen.Condition) IPluginVersionDo
	Order(conds ...field.Expr) IPluginVersionDo
	Distinct(cols ...field.Expr) IPluginVersionDo
	Omit(cols ...field.Expr) IPluginVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPluginVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPluginVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPluginVersionDo
	Group(cols ...field.Expr) IPluginVersionDo
	Having(conds ...gen.Condition) IPluginVersionDo
	Limit(limit int) IPluginVersionDo
	Offset(offset int) IPluginVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPluginVersionDo
	Unscoped() IPluginVersionDo
	Create(values ...*model.PluginVersion) error
	CreateInBatches(values []*model.PluginVersion, batchSize int) error
	Save(values ...*model.PluginVersion) error
	First() (*model.PluginVersion, error)
	Take() (*model.PluginVersion, error)
	Last() (*model.PluginVersion, error)
	Find() ([]*model.PluginVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PluginVersion, err error)
	FindInBatches(result *[]*model.PluginVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PluginVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPluginVersionDo
	Assign(attrs ...field.AssignExpr) IPluginVersionDo
	Joins(fields ...field.RelationField) IPluginVersionDo
	Preload(fields ...field.RelationField) IPluginVersionDo
	FirstOrInit() (*model.PluginVersion, error)
	FirstOrCreate() (*model.PluginVersion, error)
	FindByPage(offset int, limit int) (result []*model.PluginVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPluginVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pluginVersionDo) Debug() IPluginVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pluginVersionDo) WithContext(ctx context.Context) IPluginVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pluginVersionDo) ReadDB() IPluginVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pluginVersionDo) WriteDB() IPluginVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pluginVersionDo) Session(config *gorm.Session) IPluginVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pluginVersionDo) Clauses(conds ...clause.Expression) IPluginVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pluginVersionDo) Returning(value interface{}, columns ...string) IPluginVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pluginVersionDo) Not(conds ...gen.Condition) IPluginVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pluginVersionDo) Or(conds ...gen.Condition) IPluginVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pluginVersionDo) Select(conds ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pluginVersionDo) Where(conds ...gen.Condition) IPluginVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pluginVersionDo) Order(conds ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pluginVersionDo) Distinct(cols ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pluginVersionDo) Omit(cols ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pluginVersionDo) Join(table schema.Tabler, on ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pluginVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pluginVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pluginVersionDo) Group(cols ...field.Expr) IPluginVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pluginVersionDo) Having(conds ...gen.Condition) IPluginVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pluginVersionDo) Limit(limit int) IPluginVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pluginVersionDo) Offset(offset int) IPluginVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pluginVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPluginVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pluginVersionDo) Unscoped() IPluginVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pluginVersionDo) Create(values ...*model.PluginVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pluginVersionDo) CreateInBatches(values []*model.PluginVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pluginVersionDo) Save(values ...*model.PluginVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pluginVersionDo) First() (*model.PluginVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginVersion), nil
	}
}

func (p pluginVersionDo) Take() (*model.PluginVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginVersion), nil
	}
}

func (p pluginVersionDo) Last() (*model.PluginVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginVersion), nil
	}
}

func (p pluginVersionDo) Find() ([]*model.PluginVersion, error) {
	result, err := p.DO.Find()
	return result.([]*model.PluginVersion), err
}

func (p pluginVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PluginVersion, err error) {
	buf := make([]*model.PluginVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pluginVersionDo) FindInBatches(result *[]*model.PluginVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pluginVersionDo) Attrs(attrs ...field.AssignExpr) IPluginVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pluginVersionDo) Assign(attrs ...field.AssignExpr) IPluginVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pluginVersionDo) Joins(fields ...field.RelationField) IPluginVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pluginVersionDo) Preload(fields ...field.RelationField) IPluginVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pluginVersionDo) FirstOrInit() (*model.PluginVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginVersion), nil
	}
}

func (p pluginVersionDo) FirstOrCreate() (*model.PluginVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginVersion), nil
	}
}

func (p pluginVersionDo) FindByPage(offset int, limit int) (result []*model.PluginVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pluginVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pluginVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pluginVersionDo) Delete(models ...*model.PluginVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pluginVersionDo) withDO(do gen.Dao) *pluginVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
