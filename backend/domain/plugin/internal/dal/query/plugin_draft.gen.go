// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/plugin/internal/dal/model"
)

func newPluginDraft(db *gorm.DB, opts ...gen.DOOption) pluginDraft {
	_pluginDraft := pluginDraft{}

	_pluginDraft.pluginDraftDo.UseDB(db, opts...)
	_pluginDraft.pluginDraftDo.UseModel(&model.PluginDraft{})

	tableName := _pluginDraft.pluginDraftDo.TableName()
	_pluginDraft.ALL = field.NewAsterisk(tableName)
	_pluginDraft.ID = field.NewInt64(tableName, "id")
	_pluginDraft.SpaceID = field.NewInt64(tableName, "space_id")
	_pluginDraft.DeveloperID = field.NewInt64(tableName, "developer_id")
	_pluginDraft.AppID = field.NewInt64(tableName, "app_id")
	_pluginDraft.IconURI = field.NewString(tableName, "icon_uri")
	_pluginDraft.ServerURL = field.NewString(tableName, "server_url")
	_pluginDraft.PluginType = field.NewInt32(tableName, "plugin_type")
	_pluginDraft.CreatedAt = field.NewInt64(tableName, "created_at")
	_pluginDraft.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_pluginDraft.DeletedAt = field.NewField(tableName, "deleted_at")
	_pluginDraft.Manifest = field.NewField(tableName, "manifest")
	_pluginDraft.OpenapiDoc = field.NewField(tableName, "openapi_doc")

	_pluginDraft.fillFieldMap()

	return _pluginDraft
}

// pluginDraft Draft Plugin
type pluginDraft struct {
	pluginDraftDo

	ALL         field.Asterisk
	ID          field.Int64  // Plugin ID
	SpaceID     field.Int64  // Space ID
	DeveloperID field.Int64  // Developer ID
	AppID       field.Int64  // Application ID
	IconURI     field.String // Icon URI
	ServerURL   field.String // Server URL
	PluginType  field.Int32  // Plugin Type, 1:http, 6:local
	CreatedAt   field.Int64  // Create Time in Milliseconds
	UpdatedAt   field.Int64  // Update Time in Milliseconds
	DeletedAt   field.Field  // Delete Time
	Manifest    field.Field  // Plugin Manifest
	OpenapiDoc  field.Field  // OpenAPI Document, only stores the root

	fieldMap map[string]field.Expr
}

func (p pluginDraft) Table(newTableName string) *pluginDraft {
	p.pluginDraftDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pluginDraft) As(alias string) *pluginDraft {
	p.pluginDraftDo.DO = *(p.pluginDraftDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pluginDraft) updateTableName(table string) *pluginDraft {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.SpaceID = field.NewInt64(table, "space_id")
	p.DeveloperID = field.NewInt64(table, "developer_id")
	p.AppID = field.NewInt64(table, "app_id")
	p.IconURI = field.NewString(table, "icon_uri")
	p.ServerURL = field.NewString(table, "server_url")
	p.PluginType = field.NewInt32(table, "plugin_type")
	p.CreatedAt = field.NewInt64(table, "created_at")
	p.UpdatedAt = field.NewInt64(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.Manifest = field.NewField(table, "manifest")
	p.OpenapiDoc = field.NewField(table, "openapi_doc")

	p.fillFieldMap()

	return p
}

func (p *pluginDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pluginDraft) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 12)
	p.fieldMap["id"] = p.ID
	p.fieldMap["space_id"] = p.SpaceID
	p.fieldMap["developer_id"] = p.DeveloperID
	p.fieldMap["app_id"] = p.AppID
	p.fieldMap["icon_uri"] = p.IconURI
	p.fieldMap["server_url"] = p.ServerURL
	p.fieldMap["plugin_type"] = p.PluginType
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["manifest"] = p.Manifest
	p.fieldMap["openapi_doc"] = p.OpenapiDoc
}

func (p pluginDraft) clone(db *gorm.DB) pluginDraft {
	p.pluginDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pluginDraft) replaceDB(db *gorm.DB) pluginDraft {
	p.pluginDraftDo.ReplaceDB(db)
	return p
}

type pluginDraftDo struct{ gen.DO }

type IPluginDraftDo interface {
	gen.SubQuery
	Debug() IPluginDraftDo
	WithContext(ctx context.Context) IPluginDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPluginDraftDo
	WriteDB() IPluginDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPluginDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPluginDraftDo
	Not(conds ...gen.Condition) IPluginDraftDo
	Or(conds ...gen.Condition) IPluginDraftDo
	Select(conds ...field.Expr) IPluginDraftDo
	Where(conds ...gen.Condition) IPluginDraftDo
	Order(conds ...field.Expr) IPluginDraftDo
	Distinct(cols ...field.Expr) IPluginDraftDo
	Omit(cols ...field.Expr) IPluginDraftDo
	Join(table schema.Tabler, on ...field.Expr) IPluginDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPluginDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPluginDraftDo
	Group(cols ...field.Expr) IPluginDraftDo
	Having(conds ...gen.Condition) IPluginDraftDo
	Limit(limit int) IPluginDraftDo
	Offset(offset int) IPluginDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPluginDraftDo
	Unscoped() IPluginDraftDo
	Create(values ...*model.PluginDraft) error
	CreateInBatches(values []*model.PluginDraft, batchSize int) error
	Save(values ...*model.PluginDraft) error
	First() (*model.PluginDraft, error)
	Take() (*model.PluginDraft, error)
	Last() (*model.PluginDraft, error)
	Find() ([]*model.PluginDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PluginDraft, err error)
	FindInBatches(result *[]*model.PluginDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PluginDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPluginDraftDo
	Assign(attrs ...field.AssignExpr) IPluginDraftDo
	Joins(fields ...field.RelationField) IPluginDraftDo
	Preload(fields ...field.RelationField) IPluginDraftDo
	FirstOrInit() (*model.PluginDraft, error)
	FirstOrCreate() (*model.PluginDraft, error)
	FindByPage(offset int, limit int) (result []*model.PluginDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPluginDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pluginDraftDo) Debug() IPluginDraftDo {
	return p.withDO(p.DO.Debug())
}

func (p pluginDraftDo) WithContext(ctx context.Context) IPluginDraftDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pluginDraftDo) ReadDB() IPluginDraftDo {
	return p.Clauses(dbresolver.Read)
}

func (p pluginDraftDo) WriteDB() IPluginDraftDo {
	return p.Clauses(dbresolver.Write)
}

func (p pluginDraftDo) Session(config *gorm.Session) IPluginDraftDo {
	return p.withDO(p.DO.Session(config))
}

func (p pluginDraftDo) Clauses(conds ...clause.Expression) IPluginDraftDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pluginDraftDo) Returning(value interface{}, columns ...string) IPluginDraftDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pluginDraftDo) Not(conds ...gen.Condition) IPluginDraftDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pluginDraftDo) Or(conds ...gen.Condition) IPluginDraftDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pluginDraftDo) Select(conds ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pluginDraftDo) Where(conds ...gen.Condition) IPluginDraftDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pluginDraftDo) Order(conds ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pluginDraftDo) Distinct(cols ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pluginDraftDo) Omit(cols ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pluginDraftDo) Join(table schema.Tabler, on ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pluginDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pluginDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pluginDraftDo) Group(cols ...field.Expr) IPluginDraftDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pluginDraftDo) Having(conds ...gen.Condition) IPluginDraftDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pluginDraftDo) Limit(limit int) IPluginDraftDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pluginDraftDo) Offset(offset int) IPluginDraftDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pluginDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPluginDraftDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pluginDraftDo) Unscoped() IPluginDraftDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pluginDraftDo) Create(values ...*model.PluginDraft) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pluginDraftDo) CreateInBatches(values []*model.PluginDraft, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pluginDraftDo) Save(values ...*model.PluginDraft) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pluginDraftDo) First() (*model.PluginDraft, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginDraft), nil
	}
}

func (p pluginDraftDo) Take() (*model.PluginDraft, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginDraft), nil
	}
}

func (p pluginDraftDo) Last() (*model.PluginDraft, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginDraft), nil
	}
}

func (p pluginDraftDo) Find() ([]*model.PluginDraft, error) {
	result, err := p.DO.Find()
	return result.([]*model.PluginDraft), err
}

func (p pluginDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PluginDraft, err error) {
	buf := make([]*model.PluginDraft, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pluginDraftDo) FindInBatches(result *[]*model.PluginDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pluginDraftDo) Attrs(attrs ...field.AssignExpr) IPluginDraftDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pluginDraftDo) Assign(attrs ...field.AssignExpr) IPluginDraftDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pluginDraftDo) Joins(fields ...field.RelationField) IPluginDraftDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pluginDraftDo) Preload(fields ...field.RelationField) IPluginDraftDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pluginDraftDo) FirstOrInit() (*model.PluginDraft, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginDraft), nil
	}
}

func (p pluginDraftDo) FirstOrCreate() (*model.PluginDraft, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PluginDraft), nil
	}
}

func (p pluginDraftDo) FindByPage(offset int, limit int) (result []*model.PluginDraft, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pluginDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pluginDraftDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pluginDraftDo) Delete(models ...*model.PluginDraft) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pluginDraftDo) withDO(do gen.Dao) *pluginDraftDo {
	p.DO = *do.(*gen.DO)
	return p
}
