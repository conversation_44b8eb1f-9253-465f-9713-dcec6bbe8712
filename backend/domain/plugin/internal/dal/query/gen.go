// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                = new(Query)
	AgentToolDraft   *agentToolDraft
	AgentToolVersion *agentToolVersion
	Plugin           *plugin
	PluginDraft      *pluginDraft
	PluginOauthAuth  *pluginOauthAuth
	PluginVersion    *pluginVersion
	Tool             *tool
	ToolDraft        *toolDraft
	ToolVersion      *toolVersion
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	AgentToolDraft = &Q.AgentToolDraft
	AgentToolVersion = &Q.AgentToolVersion
	Plugin = &Q.Plugin
	PluginDraft = &Q.PluginDraft
	PluginOauthAuth = &Q.PluginOauthAuth
	PluginVersion = &Q.PluginVersion
	Tool = &Q.Tool
	ToolDraft = &Q.ToolDraft
	ToolVersion = &Q.ToolVersion
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:               db,
		AgentToolDraft:   newAgentToolDraft(db, opts...),
		AgentToolVersion: newAgentToolVersion(db, opts...),
		Plugin:           newPlugin(db, opts...),
		PluginDraft:      newPluginDraft(db, opts...),
		PluginOauthAuth:  newPluginOauthAuth(db, opts...),
		PluginVersion:    newPluginVersion(db, opts...),
		Tool:             newTool(db, opts...),
		ToolDraft:        newToolDraft(db, opts...),
		ToolVersion:      newToolVersion(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	AgentToolDraft   agentToolDraft
	AgentToolVersion agentToolVersion
	Plugin           plugin
	PluginDraft      pluginDraft
	PluginOauthAuth  pluginOauthAuth
	PluginVersion    pluginVersion
	Tool             tool
	ToolDraft        toolDraft
	ToolVersion      toolVersion
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:               db,
		AgentToolDraft:   q.AgentToolDraft.clone(db),
		AgentToolVersion: q.AgentToolVersion.clone(db),
		Plugin:           q.Plugin.clone(db),
		PluginDraft:      q.PluginDraft.clone(db),
		PluginOauthAuth:  q.PluginOauthAuth.clone(db),
		PluginVersion:    q.PluginVersion.clone(db),
		Tool:             q.Tool.clone(db),
		ToolDraft:        q.ToolDraft.clone(db),
		ToolVersion:      q.ToolVersion.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:               db,
		AgentToolDraft:   q.AgentToolDraft.replaceDB(db),
		AgentToolVersion: q.AgentToolVersion.replaceDB(db),
		Plugin:           q.Plugin.replaceDB(db),
		PluginDraft:      q.PluginDraft.replaceDB(db),
		PluginOauthAuth:  q.PluginOauthAuth.replaceDB(db),
		PluginVersion:    q.PluginVersion.replaceDB(db),
		Tool:             q.Tool.replaceDB(db),
		ToolDraft:        q.ToolDraft.replaceDB(db),
		ToolVersion:      q.ToolVersion.replaceDB(db),
	}
}

type queryCtx struct {
	AgentToolDraft   IAgentToolDraftDo
	AgentToolVersion IAgentToolVersionDo
	Plugin           IPluginDo
	PluginDraft      IPluginDraftDo
	PluginOauthAuth  IPluginOauthAuthDo
	PluginVersion    IPluginVersionDo
	Tool             IToolDo
	ToolDraft        IToolDraftDo
	ToolVersion      IToolVersionDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		AgentToolDraft:   q.AgentToolDraft.WithContext(ctx),
		AgentToolVersion: q.AgentToolVersion.WithContext(ctx),
		Plugin:           q.Plugin.WithContext(ctx),
		PluginDraft:      q.PluginDraft.WithContext(ctx),
		PluginOauthAuth:  q.PluginOauthAuth.WithContext(ctx),
		PluginVersion:    q.PluginVersion.WithContext(ctx),
		Tool:             q.Tool.WithContext(ctx),
		ToolDraft:        q.ToolDraft.WithContext(ctx),
		ToolVersion:      q.ToolVersion.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
