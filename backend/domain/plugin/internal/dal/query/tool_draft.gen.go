// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/plugin/internal/dal/model"
)

func newToolDraft(db *gorm.DB, opts ...gen.DOOption) toolDraft {
	_toolDraft := toolDraft{}

	_toolDraft.toolDraftDo.UseDB(db, opts...)
	_toolDraft.toolDraftDo.UseModel(&model.ToolDraft{})

	tableName := _toolDraft.toolDraftDo.TableName()
	_toolDraft.ALL = field.NewAsterisk(tableName)
	_toolDraft.ID = field.NewInt64(tableName, "id")
	_toolDraft.PluginID = field.NewInt64(tableName, "plugin_id")
	_toolDraft.CreatedAt = field.NewInt64(tableName, "created_at")
	_toolDraft.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_toolDraft.SubURL = field.NewString(tableName, "sub_url")
	_toolDraft.Method = field.NewString(tableName, "method")
	_toolDraft.Operation = field.NewField(tableName, "operation")
	_toolDraft.DebugStatus = field.NewInt32(tableName, "debug_status")
	_toolDraft.ActivatedStatus = field.NewInt32(tableName, "activated_status")

	_toolDraft.fillFieldMap()

	return _toolDraft
}

// toolDraft Draft Tool
type toolDraft struct {
	toolDraftDo

	ALL             field.Asterisk
	ID              field.Int64  // Tool ID
	PluginID        field.Int64  // Plugin ID
	CreatedAt       field.Int64  // Create Time in Milliseconds
	UpdatedAt       field.Int64  // Update Time in Milliseconds
	SubURL          field.String // Sub URL Path
	Method          field.String // HTTP Request Method
	Operation       field.Field  // Tool Openapi Operation Schema
	DebugStatus     field.Int32  // 0:not pass; 1:pass
	ActivatedStatus field.Int32  // 0:activated; 1:deactivated

	fieldMap map[string]field.Expr
}

func (t toolDraft) Table(newTableName string) *toolDraft {
	t.toolDraftDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t toolDraft) As(alias string) *toolDraft {
	t.toolDraftDo.DO = *(t.toolDraftDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *toolDraft) updateTableName(table string) *toolDraft {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.PluginID = field.NewInt64(table, "plugin_id")
	t.CreatedAt = field.NewInt64(table, "created_at")
	t.UpdatedAt = field.NewInt64(table, "updated_at")
	t.SubURL = field.NewString(table, "sub_url")
	t.Method = field.NewString(table, "method")
	t.Operation = field.NewField(table, "operation")
	t.DebugStatus = field.NewInt32(table, "debug_status")
	t.ActivatedStatus = field.NewInt32(table, "activated_status")

	t.fillFieldMap()

	return t
}

func (t *toolDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *toolDraft) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 9)
	t.fieldMap["id"] = t.ID
	t.fieldMap["plugin_id"] = t.PluginID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["sub_url"] = t.SubURL
	t.fieldMap["method"] = t.Method
	t.fieldMap["operation"] = t.Operation
	t.fieldMap["debug_status"] = t.DebugStatus
	t.fieldMap["activated_status"] = t.ActivatedStatus
}

func (t toolDraft) clone(db *gorm.DB) toolDraft {
	t.toolDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t toolDraft) replaceDB(db *gorm.DB) toolDraft {
	t.toolDraftDo.ReplaceDB(db)
	return t
}

type toolDraftDo struct{ gen.DO }

type IToolDraftDo interface {
	gen.SubQuery
	Debug() IToolDraftDo
	WithContext(ctx context.Context) IToolDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IToolDraftDo
	WriteDB() IToolDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IToolDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IToolDraftDo
	Not(conds ...gen.Condition) IToolDraftDo
	Or(conds ...gen.Condition) IToolDraftDo
	Select(conds ...field.Expr) IToolDraftDo
	Where(conds ...gen.Condition) IToolDraftDo
	Order(conds ...field.Expr) IToolDraftDo
	Distinct(cols ...field.Expr) IToolDraftDo
	Omit(cols ...field.Expr) IToolDraftDo
	Join(table schema.Tabler, on ...field.Expr) IToolDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IToolDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) IToolDraftDo
	Group(cols ...field.Expr) IToolDraftDo
	Having(conds ...gen.Condition) IToolDraftDo
	Limit(limit int) IToolDraftDo
	Offset(offset int) IToolDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IToolDraftDo
	Unscoped() IToolDraftDo
	Create(values ...*model.ToolDraft) error
	CreateInBatches(values []*model.ToolDraft, batchSize int) error
	Save(values ...*model.ToolDraft) error
	First() (*model.ToolDraft, error)
	Take() (*model.ToolDraft, error)
	Last() (*model.ToolDraft, error)
	Find() ([]*model.ToolDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ToolDraft, err error)
	FindInBatches(result *[]*model.ToolDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ToolDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IToolDraftDo
	Assign(attrs ...field.AssignExpr) IToolDraftDo
	Joins(fields ...field.RelationField) IToolDraftDo
	Preload(fields ...field.RelationField) IToolDraftDo
	FirstOrInit() (*model.ToolDraft, error)
	FirstOrCreate() (*model.ToolDraft, error)
	FindByPage(offset int, limit int) (result []*model.ToolDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IToolDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t toolDraftDo) Debug() IToolDraftDo {
	return t.withDO(t.DO.Debug())
}

func (t toolDraftDo) WithContext(ctx context.Context) IToolDraftDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t toolDraftDo) ReadDB() IToolDraftDo {
	return t.Clauses(dbresolver.Read)
}

func (t toolDraftDo) WriteDB() IToolDraftDo {
	return t.Clauses(dbresolver.Write)
}

func (t toolDraftDo) Session(config *gorm.Session) IToolDraftDo {
	return t.withDO(t.DO.Session(config))
}

func (t toolDraftDo) Clauses(conds ...clause.Expression) IToolDraftDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t toolDraftDo) Returning(value interface{}, columns ...string) IToolDraftDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t toolDraftDo) Not(conds ...gen.Condition) IToolDraftDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t toolDraftDo) Or(conds ...gen.Condition) IToolDraftDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t toolDraftDo) Select(conds ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t toolDraftDo) Where(conds ...gen.Condition) IToolDraftDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t toolDraftDo) Order(conds ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t toolDraftDo) Distinct(cols ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t toolDraftDo) Omit(cols ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t toolDraftDo) Join(table schema.Tabler, on ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t toolDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t toolDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t toolDraftDo) Group(cols ...field.Expr) IToolDraftDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t toolDraftDo) Having(conds ...gen.Condition) IToolDraftDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t toolDraftDo) Limit(limit int) IToolDraftDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t toolDraftDo) Offset(offset int) IToolDraftDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t toolDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IToolDraftDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t toolDraftDo) Unscoped() IToolDraftDo {
	return t.withDO(t.DO.Unscoped())
}

func (t toolDraftDo) Create(values ...*model.ToolDraft) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t toolDraftDo) CreateInBatches(values []*model.ToolDraft, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t toolDraftDo) Save(values ...*model.ToolDraft) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t toolDraftDo) First() (*model.ToolDraft, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ToolDraft), nil
	}
}

func (t toolDraftDo) Take() (*model.ToolDraft, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ToolDraft), nil
	}
}

func (t toolDraftDo) Last() (*model.ToolDraft, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ToolDraft), nil
	}
}

func (t toolDraftDo) Find() ([]*model.ToolDraft, error) {
	result, err := t.DO.Find()
	return result.([]*model.ToolDraft), err
}

func (t toolDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ToolDraft, err error) {
	buf := make([]*model.ToolDraft, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t toolDraftDo) FindInBatches(result *[]*model.ToolDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t toolDraftDo) Attrs(attrs ...field.AssignExpr) IToolDraftDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t toolDraftDo) Assign(attrs ...field.AssignExpr) IToolDraftDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t toolDraftDo) Joins(fields ...field.RelationField) IToolDraftDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t toolDraftDo) Preload(fields ...field.RelationField) IToolDraftDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t toolDraftDo) FirstOrInit() (*model.ToolDraft, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ToolDraft), nil
	}
}

func (t toolDraftDo) FirstOrCreate() (*model.ToolDraft, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ToolDraft), nil
	}
}

func (t toolDraftDo) FindByPage(offset int, limit int) (result []*model.ToolDraft, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t toolDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t toolDraftDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t toolDraftDo) Delete(models ...*model.ToolDraft) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *toolDraftDo) withDO(do gen.Dao) *toolDraftDo {
	t.DO = *do.(*gen.DO)
	return t
}
