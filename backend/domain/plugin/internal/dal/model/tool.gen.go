// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "github.com/coze-dev/coze-studio/backend/api/model/crossdomain/plugin"

const TableNameTool = "tool"

// Tool Latest Tool
type Tool struct {
	ID              int64                     `gorm:"column:id;primaryKey;comment:Tool ID" json:"id"`                                                        // Tool ID
	PluginID        int64                     `gorm:"column:plugin_id;not null;comment:Plugin ID" json:"plugin_id"`                                          // Plugin ID
	CreatedAt       int64                     `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt       int64                     `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	Version         string                    `gorm:"column:version;not null;comment:Tool Version, e.g. v1.0.0" json:"version"`                              // Tool Version, e.g. v1.0.0
	SubURL          string                    `gorm:"column:sub_url;not null;comment:Sub URL Path" json:"sub_url"`                                           // Sub URL Path
	Method          string                    `gorm:"column:method;not null;comment:HTTP Request Method" json:"method"`                                      // HTTP Request Method
	Operation       *plugin.Openapi3Operation `gorm:"column:operation;comment:Tool Openapi Operation Schema;serializer:json" json:"operation"`               // Tool Openapi Operation Schema
	ActivatedStatus int32                     `gorm:"column:activated_status;not null;comment:0:activated; 1:deactivated" json:"activated_status"`           // 0:activated; 1:deactivated
}

// TableName Tool's table name
func (*Tool) TableName() string {
	return TableNameTool
}
