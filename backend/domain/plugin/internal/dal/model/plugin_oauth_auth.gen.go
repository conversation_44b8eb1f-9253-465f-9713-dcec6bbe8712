// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "github.com/coze-dev/coze-studio/backend/api/model/crossdomain/plugin"

const TableNamePluginOauthAuth = "plugin_oauth_auth"

// PluginOauthAuth Plugin OAuth Authorization Code Info
type PluginOauthAuth struct {
	ID                 int64                                `gorm:"column:id;primaryKey;comment:Primary Key" json:"id"`                                                        // Primary Key
	UserID             string                               `gorm:"column:user_id;not null;comment:User ID" json:"user_id"`                                                    // User ID
	PluginID           int64                                `gorm:"column:plugin_id;not null;comment:Plugin ID" json:"plugin_id"`                                              // Plugin ID
	IsDraft            bool                                 `gorm:"column:is_draft;not null;comment:Is Draft Plugin" json:"is_draft"`                                          // Is Draft Plugin
	OauthConfig        *plugin.OAuthAuthorizationCodeConfig `gorm:"column:oauth_config;comment:Authorization Code OAuth Config;serializer:json" json:"oauth_config"`           // Authorization Code OAuth Config
	AccessToken        string                               `gorm:"column:access_token;not null;comment:Access Token" json:"access_token"`                                     // Access Token
	RefreshToken       string                               `gorm:"column:refresh_token;not null;comment:Refresh Token" json:"refresh_token"`                                  // Refresh Token
	TokenExpiredAt     int64                                `gorm:"column:token_expired_at;comment:Token Expired in Milliseconds" json:"token_expired_at"`                     // Token Expired in Milliseconds
	NextTokenRefreshAt int64                                `gorm:"column:next_token_refresh_at;comment:Next Token Refresh Time in Milliseconds" json:"next_token_refresh_at"` // Next Token Refresh Time in Milliseconds
	LastActiveAt       int64                                `gorm:"column:last_active_at;comment:Last active time in Milliseconds" json:"last_active_at"`                      // Last active time in Milliseconds
	CreatedAt          int64                                `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"`     // Create Time in Milliseconds
	UpdatedAt          int64                                `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"`     // Update Time in Milliseconds
}

// TableName PluginOauthAuth's table name
func (*PluginOauthAuth) TableName() string {
	return TableNamePluginOauthAuth
}
