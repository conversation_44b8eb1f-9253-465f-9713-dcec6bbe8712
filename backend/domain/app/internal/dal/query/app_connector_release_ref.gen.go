// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/app/internal/dal/model"
)

func newAppConnectorReleaseRef(db *gorm.DB, opts ...gen.DOOption) appConnectorReleaseRef {
	_appConnectorReleaseRef := appConnectorReleaseRef{}

	_appConnectorReleaseRef.appConnectorReleaseRefDo.UseDB(db, opts...)
	_appConnectorReleaseRef.appConnectorReleaseRefDo.UseModel(&model.AppConnectorReleaseRef{})

	tableName := _appConnectorReleaseRef.appConnectorReleaseRefDo.TableName()
	_appConnectorReleaseRef.ALL = field.NewAsterisk(tableName)
	_appConnectorReleaseRef.ID = field.NewInt64(tableName, "id")
	_appConnectorReleaseRef.RecordID = field.NewInt64(tableName, "record_id")
	_appConnectorReleaseRef.ConnectorID = field.NewInt64(tableName, "connector_id")
	_appConnectorReleaseRef.PublishConfig = field.NewField(tableName, "publish_config")
	_appConnectorReleaseRef.PublishStatus = field.NewInt32(tableName, "publish_status")
	_appConnectorReleaseRef.CreatedAt = field.NewInt64(tableName, "created_at")
	_appConnectorReleaseRef.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_appConnectorReleaseRef.fillFieldMap()

	return _appConnectorReleaseRef
}

// appConnectorReleaseRef Connector Release Record Reference
type appConnectorReleaseRef struct {
	appConnectorReleaseRefDo

	ALL           field.Asterisk
	ID            field.Int64 // Primary Key
	RecordID      field.Int64 // Publish Record ID
	ConnectorID   field.Int64 // Publish Connector ID
	PublishConfig field.Field // Publish Configuration
	PublishStatus field.Int32 // Publish Status
	CreatedAt     field.Int64 // Create Time in Milliseconds
	UpdatedAt     field.Int64 // Update Time in Milliseconds

	fieldMap map[string]field.Expr
}

func (a appConnectorReleaseRef) Table(newTableName string) *appConnectorReleaseRef {
	a.appConnectorReleaseRefDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appConnectorReleaseRef) As(alias string) *appConnectorReleaseRef {
	a.appConnectorReleaseRefDo.DO = *(a.appConnectorReleaseRefDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appConnectorReleaseRef) updateTableName(table string) *appConnectorReleaseRef {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.RecordID = field.NewInt64(table, "record_id")
	a.ConnectorID = field.NewInt64(table, "connector_id")
	a.PublishConfig = field.NewField(table, "publish_config")
	a.PublishStatus = field.NewInt32(table, "publish_status")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *appConnectorReleaseRef) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appConnectorReleaseRef) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 7)
	a.fieldMap["id"] = a.ID
	a.fieldMap["record_id"] = a.RecordID
	a.fieldMap["connector_id"] = a.ConnectorID
	a.fieldMap["publish_config"] = a.PublishConfig
	a.fieldMap["publish_status"] = a.PublishStatus
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a appConnectorReleaseRef) clone(db *gorm.DB) appConnectorReleaseRef {
	a.appConnectorReleaseRefDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appConnectorReleaseRef) replaceDB(db *gorm.DB) appConnectorReleaseRef {
	a.appConnectorReleaseRefDo.ReplaceDB(db)
	return a
}

type appConnectorReleaseRefDo struct{ gen.DO }

type IAppConnectorReleaseRefDo interface {
	gen.SubQuery
	Debug() IAppConnectorReleaseRefDo
	WithContext(ctx context.Context) IAppConnectorReleaseRefDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppConnectorReleaseRefDo
	WriteDB() IAppConnectorReleaseRefDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppConnectorReleaseRefDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppConnectorReleaseRefDo
	Not(conds ...gen.Condition) IAppConnectorReleaseRefDo
	Or(conds ...gen.Condition) IAppConnectorReleaseRefDo
	Select(conds ...field.Expr) IAppConnectorReleaseRefDo
	Where(conds ...gen.Condition) IAppConnectorReleaseRefDo
	Order(conds ...field.Expr) IAppConnectorReleaseRefDo
	Distinct(cols ...field.Expr) IAppConnectorReleaseRefDo
	Omit(cols ...field.Expr) IAppConnectorReleaseRefDo
	Join(table schema.Tabler, on ...field.Expr) IAppConnectorReleaseRefDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppConnectorReleaseRefDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppConnectorReleaseRefDo
	Group(cols ...field.Expr) IAppConnectorReleaseRefDo
	Having(conds ...gen.Condition) IAppConnectorReleaseRefDo
	Limit(limit int) IAppConnectorReleaseRefDo
	Offset(offset int) IAppConnectorReleaseRefDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppConnectorReleaseRefDo
	Unscoped() IAppConnectorReleaseRefDo
	Create(values ...*model.AppConnectorReleaseRef) error
	CreateInBatches(values []*model.AppConnectorReleaseRef, batchSize int) error
	Save(values ...*model.AppConnectorReleaseRef) error
	First() (*model.AppConnectorReleaseRef, error)
	Take() (*model.AppConnectorReleaseRef, error)
	Last() (*model.AppConnectorReleaseRef, error)
	Find() ([]*model.AppConnectorReleaseRef, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppConnectorReleaseRef, err error)
	FindInBatches(result *[]*model.AppConnectorReleaseRef, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppConnectorReleaseRef) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppConnectorReleaseRefDo
	Assign(attrs ...field.AssignExpr) IAppConnectorReleaseRefDo
	Joins(fields ...field.RelationField) IAppConnectorReleaseRefDo
	Preload(fields ...field.RelationField) IAppConnectorReleaseRefDo
	FirstOrInit() (*model.AppConnectorReleaseRef, error)
	FirstOrCreate() (*model.AppConnectorReleaseRef, error)
	FindByPage(offset int, limit int) (result []*model.AppConnectorReleaseRef, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppConnectorReleaseRefDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appConnectorReleaseRefDo) Debug() IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Debug())
}

func (a appConnectorReleaseRefDo) WithContext(ctx context.Context) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appConnectorReleaseRefDo) ReadDB() IAppConnectorReleaseRefDo {
	return a.Clauses(dbresolver.Read)
}

func (a appConnectorReleaseRefDo) WriteDB() IAppConnectorReleaseRefDo {
	return a.Clauses(dbresolver.Write)
}

func (a appConnectorReleaseRefDo) Session(config *gorm.Session) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Session(config))
}

func (a appConnectorReleaseRefDo) Clauses(conds ...clause.Expression) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appConnectorReleaseRefDo) Returning(value interface{}, columns ...string) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appConnectorReleaseRefDo) Not(conds ...gen.Condition) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appConnectorReleaseRefDo) Or(conds ...gen.Condition) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appConnectorReleaseRefDo) Select(conds ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appConnectorReleaseRefDo) Where(conds ...gen.Condition) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appConnectorReleaseRefDo) Order(conds ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appConnectorReleaseRefDo) Distinct(cols ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appConnectorReleaseRefDo) Omit(cols ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appConnectorReleaseRefDo) Join(table schema.Tabler, on ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appConnectorReleaseRefDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appConnectorReleaseRefDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appConnectorReleaseRefDo) Group(cols ...field.Expr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appConnectorReleaseRefDo) Having(conds ...gen.Condition) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appConnectorReleaseRefDo) Limit(limit int) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appConnectorReleaseRefDo) Offset(offset int) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appConnectorReleaseRefDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appConnectorReleaseRefDo) Unscoped() IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appConnectorReleaseRefDo) Create(values ...*model.AppConnectorReleaseRef) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appConnectorReleaseRefDo) CreateInBatches(values []*model.AppConnectorReleaseRef, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appConnectorReleaseRefDo) Save(values ...*model.AppConnectorReleaseRef) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appConnectorReleaseRefDo) First() (*model.AppConnectorReleaseRef, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConnectorReleaseRef), nil
	}
}

func (a appConnectorReleaseRefDo) Take() (*model.AppConnectorReleaseRef, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConnectorReleaseRef), nil
	}
}

func (a appConnectorReleaseRefDo) Last() (*model.AppConnectorReleaseRef, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConnectorReleaseRef), nil
	}
}

func (a appConnectorReleaseRefDo) Find() ([]*model.AppConnectorReleaseRef, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppConnectorReleaseRef), err
}

func (a appConnectorReleaseRefDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppConnectorReleaseRef, err error) {
	buf := make([]*model.AppConnectorReleaseRef, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appConnectorReleaseRefDo) FindInBatches(result *[]*model.AppConnectorReleaseRef, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appConnectorReleaseRefDo) Attrs(attrs ...field.AssignExpr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appConnectorReleaseRefDo) Assign(attrs ...field.AssignExpr) IAppConnectorReleaseRefDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appConnectorReleaseRefDo) Joins(fields ...field.RelationField) IAppConnectorReleaseRefDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appConnectorReleaseRefDo) Preload(fields ...field.RelationField) IAppConnectorReleaseRefDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appConnectorReleaseRefDo) FirstOrInit() (*model.AppConnectorReleaseRef, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConnectorReleaseRef), nil
	}
}

func (a appConnectorReleaseRefDo) FirstOrCreate() (*model.AppConnectorReleaseRef, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppConnectorReleaseRef), nil
	}
}

func (a appConnectorReleaseRefDo) FindByPage(offset int, limit int) (result []*model.AppConnectorReleaseRef, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appConnectorReleaseRefDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appConnectorReleaseRefDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appConnectorReleaseRefDo) Delete(models ...*model.AppConnectorReleaseRef) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appConnectorReleaseRefDo) withDO(do gen.Dao) *appConnectorReleaseRefDo {
	a.DO = *do.(*gen.DO)
	return a
}
