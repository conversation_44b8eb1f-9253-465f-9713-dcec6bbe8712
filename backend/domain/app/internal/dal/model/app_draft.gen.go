// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameAppDraft = "app_draft"

// AppDraft Draft Application
type AppDraft struct {
	ID          int64          `gorm:"column:id;primaryKey;comment:APP ID" json:"id"`                                                         // APP ID
	SpaceID     int64          `gorm:"column:space_id;not null;comment:Space ID" json:"space_id"`                                             // Space ID
	OwnerID     int64          `gorm:"column:owner_id;not null;comment:Owner ID" json:"owner_id"`                                             // Owner ID
	IconURI     string         `gorm:"column:icon_uri;not null;comment:Icon URI" json:"icon_uri"`                                             // Icon URI
	Name        string         `gorm:"column:name;not null;comment:Application Name" json:"name"`                                             // Application Name
	Description string         `gorm:"column:description;comment:Application Description" json:"description"`                                 // Application Description
	CreatedAt   int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt   int64          `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                               // Delete Time
}

// TableName AppDraft's table name
func (*AppDraft) TableName() string {
	return TableNameAppDraft
}
