// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/agent/singleagent/internal/dal/model"
)

func newSingleAgentPublish(db *gorm.DB, opts ...gen.DOOption) singleAgentPublish {
	_singleAgentPublish := singleAgentPublish{}

	_singleAgentPublish.singleAgentPublishDo.UseDB(db, opts...)
	_singleAgentPublish.singleAgentPublishDo.UseModel(&model.SingleAgentPublish{})

	tableName := _singleAgentPublish.singleAgentPublishDo.TableName()
	_singleAgentPublish.ALL = field.NewAsterisk(tableName)
	_singleAgentPublish.ID = field.NewInt64(tableName, "id")
	_singleAgentPublish.AgentID = field.NewInt64(tableName, "agent_id")
	_singleAgentPublish.PublishID = field.NewString(tableName, "publish_id")
	_singleAgentPublish.ConnectorIds = field.NewField(tableName, "connector_ids")
	_singleAgentPublish.Version = field.NewString(tableName, "version")
	_singleAgentPublish.PublishInfo = field.NewString(tableName, "publish_info")
	_singleAgentPublish.PublishTime = field.NewInt64(tableName, "publish_time")
	_singleAgentPublish.CreatedAt = field.NewInt64(tableName, "created_at")
	_singleAgentPublish.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_singleAgentPublish.CreatorID = field.NewInt64(tableName, "creator_id")
	_singleAgentPublish.Status = field.NewInt32(tableName, "status")
	_singleAgentPublish.Extra = field.NewString(tableName, "extra")

	_singleAgentPublish.fillFieldMap()

	return _singleAgentPublish
}

// singleAgentPublish bot 渠道和发布版本流水表
type singleAgentPublish struct {
	singleAgentPublishDo

	ALL          field.Asterisk
	ID           field.Int64  // 主键id
	AgentID      field.Int64  // agent_id
	PublishID    field.String // 发布 id
	ConnectorIds field.Field  // 发布的 connector_ids
	Version      field.String // Agent Version
	PublishInfo  field.String // 发布信息
	PublishTime  field.Int64  // 发布时间
	CreatedAt    field.Int64  // Create Time in Milliseconds
	UpdatedAt    field.Int64  // Update Time in Milliseconds
	CreatorID    field.Int64  // 发布人 user_id
	Status       field.Int32  // 状态 0:使用中 1:删除 3:禁用
	Extra        field.String // 扩展字段

	fieldMap map[string]field.Expr
}

func (s singleAgentPublish) Table(newTableName string) *singleAgentPublish {
	s.singleAgentPublishDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s singleAgentPublish) As(alias string) *singleAgentPublish {
	s.singleAgentPublishDo.DO = *(s.singleAgentPublishDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *singleAgentPublish) updateTableName(table string) *singleAgentPublish {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.AgentID = field.NewInt64(table, "agent_id")
	s.PublishID = field.NewString(table, "publish_id")
	s.ConnectorIds = field.NewField(table, "connector_ids")
	s.Version = field.NewString(table, "version")
	s.PublishInfo = field.NewString(table, "publish_info")
	s.PublishTime = field.NewInt64(table, "publish_time")
	s.CreatedAt = field.NewInt64(table, "created_at")
	s.UpdatedAt = field.NewInt64(table, "updated_at")
	s.CreatorID = field.NewInt64(table, "creator_id")
	s.Status = field.NewInt32(table, "status")
	s.Extra = field.NewString(table, "extra")

	s.fillFieldMap()

	return s
}

func (s *singleAgentPublish) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *singleAgentPublish) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 12)
	s.fieldMap["id"] = s.ID
	s.fieldMap["agent_id"] = s.AgentID
	s.fieldMap["publish_id"] = s.PublishID
	s.fieldMap["connector_ids"] = s.ConnectorIds
	s.fieldMap["version"] = s.Version
	s.fieldMap["publish_info"] = s.PublishInfo
	s.fieldMap["publish_time"] = s.PublishTime
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["creator_id"] = s.CreatorID
	s.fieldMap["status"] = s.Status
	s.fieldMap["extra"] = s.Extra
}

func (s singleAgentPublish) clone(db *gorm.DB) singleAgentPublish {
	s.singleAgentPublishDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s singleAgentPublish) replaceDB(db *gorm.DB) singleAgentPublish {
	s.singleAgentPublishDo.ReplaceDB(db)
	return s
}

type singleAgentPublishDo struct{ gen.DO }

type ISingleAgentPublishDo interface {
	gen.SubQuery
	Debug() ISingleAgentPublishDo
	WithContext(ctx context.Context) ISingleAgentPublishDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISingleAgentPublishDo
	WriteDB() ISingleAgentPublishDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISingleAgentPublishDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISingleAgentPublishDo
	Not(conds ...gen.Condition) ISingleAgentPublishDo
	Or(conds ...gen.Condition) ISingleAgentPublishDo
	Select(conds ...field.Expr) ISingleAgentPublishDo
	Where(conds ...gen.Condition) ISingleAgentPublishDo
	Order(conds ...field.Expr) ISingleAgentPublishDo
	Distinct(cols ...field.Expr) ISingleAgentPublishDo
	Omit(cols ...field.Expr) ISingleAgentPublishDo
	Join(table schema.Tabler, on ...field.Expr) ISingleAgentPublishDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISingleAgentPublishDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISingleAgentPublishDo
	Group(cols ...field.Expr) ISingleAgentPublishDo
	Having(conds ...gen.Condition) ISingleAgentPublishDo
	Limit(limit int) ISingleAgentPublishDo
	Offset(offset int) ISingleAgentPublishDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISingleAgentPublishDo
	Unscoped() ISingleAgentPublishDo
	Create(values ...*model.SingleAgentPublish) error
	CreateInBatches(values []*model.SingleAgentPublish, batchSize int) error
	Save(values ...*model.SingleAgentPublish) error
	First() (*model.SingleAgentPublish, error)
	Take() (*model.SingleAgentPublish, error)
	Last() (*model.SingleAgentPublish, error)
	Find() ([]*model.SingleAgentPublish, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SingleAgentPublish, err error)
	FindInBatches(result *[]*model.SingleAgentPublish, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SingleAgentPublish) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISingleAgentPublishDo
	Assign(attrs ...field.AssignExpr) ISingleAgentPublishDo
	Joins(fields ...field.RelationField) ISingleAgentPublishDo
	Preload(fields ...field.RelationField) ISingleAgentPublishDo
	FirstOrInit() (*model.SingleAgentPublish, error)
	FirstOrCreate() (*model.SingleAgentPublish, error)
	FindByPage(offset int, limit int) (result []*model.SingleAgentPublish, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISingleAgentPublishDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s singleAgentPublishDo) Debug() ISingleAgentPublishDo {
	return s.withDO(s.DO.Debug())
}

func (s singleAgentPublishDo) WithContext(ctx context.Context) ISingleAgentPublishDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s singleAgentPublishDo) ReadDB() ISingleAgentPublishDo {
	return s.Clauses(dbresolver.Read)
}

func (s singleAgentPublishDo) WriteDB() ISingleAgentPublishDo {
	return s.Clauses(dbresolver.Write)
}

func (s singleAgentPublishDo) Session(config *gorm.Session) ISingleAgentPublishDo {
	return s.withDO(s.DO.Session(config))
}

func (s singleAgentPublishDo) Clauses(conds ...clause.Expression) ISingleAgentPublishDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s singleAgentPublishDo) Returning(value interface{}, columns ...string) ISingleAgentPublishDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s singleAgentPublishDo) Not(conds ...gen.Condition) ISingleAgentPublishDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s singleAgentPublishDo) Or(conds ...gen.Condition) ISingleAgentPublishDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s singleAgentPublishDo) Select(conds ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s singleAgentPublishDo) Where(conds ...gen.Condition) ISingleAgentPublishDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s singleAgentPublishDo) Order(conds ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s singleAgentPublishDo) Distinct(cols ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s singleAgentPublishDo) Omit(cols ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s singleAgentPublishDo) Join(table schema.Tabler, on ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s singleAgentPublishDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s singleAgentPublishDo) RightJoin(table schema.Tabler, on ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s singleAgentPublishDo) Group(cols ...field.Expr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s singleAgentPublishDo) Having(conds ...gen.Condition) ISingleAgentPublishDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s singleAgentPublishDo) Limit(limit int) ISingleAgentPublishDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s singleAgentPublishDo) Offset(offset int) ISingleAgentPublishDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s singleAgentPublishDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISingleAgentPublishDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s singleAgentPublishDo) Unscoped() ISingleAgentPublishDo {
	return s.withDO(s.DO.Unscoped())
}

func (s singleAgentPublishDo) Create(values ...*model.SingleAgentPublish) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s singleAgentPublishDo) CreateInBatches(values []*model.SingleAgentPublish, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s singleAgentPublishDo) Save(values ...*model.SingleAgentPublish) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s singleAgentPublishDo) First() (*model.SingleAgentPublish, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentPublish), nil
	}
}

func (s singleAgentPublishDo) Take() (*model.SingleAgentPublish, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentPublish), nil
	}
}

func (s singleAgentPublishDo) Last() (*model.SingleAgentPublish, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentPublish), nil
	}
}

func (s singleAgentPublishDo) Find() ([]*model.SingleAgentPublish, error) {
	result, err := s.DO.Find()
	return result.([]*model.SingleAgentPublish), err
}

func (s singleAgentPublishDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SingleAgentPublish, err error) {
	buf := make([]*model.SingleAgentPublish, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s singleAgentPublishDo) FindInBatches(result *[]*model.SingleAgentPublish, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s singleAgentPublishDo) Attrs(attrs ...field.AssignExpr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s singleAgentPublishDo) Assign(attrs ...field.AssignExpr) ISingleAgentPublishDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s singleAgentPublishDo) Joins(fields ...field.RelationField) ISingleAgentPublishDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s singleAgentPublishDo) Preload(fields ...field.RelationField) ISingleAgentPublishDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s singleAgentPublishDo) FirstOrInit() (*model.SingleAgentPublish, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentPublish), nil
	}
}

func (s singleAgentPublishDo) FirstOrCreate() (*model.SingleAgentPublish, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentPublish), nil
	}
}

func (s singleAgentPublishDo) FindByPage(offset int, limit int) (result []*model.SingleAgentPublish, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s singleAgentPublishDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s singleAgentPublishDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s singleAgentPublishDo) Delete(models ...*model.SingleAgentPublish) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *singleAgentPublishDo) withDO(do gen.Dao) *singleAgentPublishDo {
	s.DO = *do.(*gen.DO)
	return s
}
