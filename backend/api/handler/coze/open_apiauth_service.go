/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/permission/openapiauth"
	openapiauthApp "github.com/coze-dev/coze-studio/backend/application/openauth"
	"github.com/coze-dev/coze-studio/backend/pkg/errorx"
	"github.com/coze-dev/coze-studio/backend/pkg/lang/ptr"
	"github.com/coze-dev/coze-studio/backend/pkg/logs"
	"github.com/coze-dev/coze-studio/backend/types/errno"
)

// GetPersonalAccessTokenAndPermission .
// @router /api/permission_api/pat/get_personal_access_token_and_permission [GET]
func GetPersonalAccessTokenAndPermission(ctx context.Context, c *app.RequestContext) {
	var err error
	var req openapiauth.GetPersonalAccessTokenAndPermissionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ID == 0 {
		invalidParamRequestResponse(c, "id is required")
		return
	}

	resp, err := openapiauthApp.OpenAuthApplication.GetPersonalAccessTokenAndPermission(ctx, &req)
	if err != nil {
		logs.CtxErrorf(ctx, "OpenAuthApplicationService.GetPersonalAccessTokenAndPermission failed, err=%v", err)
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DeletePersonalAccessTokenAndPermission .
// @router /api/permission_api/pat/delete_personal_access_token_and_permission [POST]
func DeletePersonalAccessTokenAndPermission(ctx context.Context, c *app.RequestContext) {
	var err error
	var req openapiauth.DeletePersonalAccessTokenAndPermissionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ID <= 0 {
		invalidParamRequestResponse(c, "id is required")
		return
	}

	resp, err := openapiauthApp.OpenAuthApplication.DeletePersonalAccessTokenAndPermission(ctx, &req)
	if err != nil {
		logs.CtxErrorf(ctx, "OpenAuthApplication.DeletePersonalAccessTokenAndPermission failed, err=%v", err)
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ListPersonalAccessTokens .
// @router /api/permission_api/pat/list_personal_access_tokens [GET]
func ListPersonalAccessTokens(ctx context.Context, c *app.RequestContext) {
	var err error
	var req openapiauth.ListPersonalAccessTokensRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.Page == nil || *req.Page <= 0 {
		req.Page = ptr.Of(int64(1))
	}
	if req.Size == nil || *req.Size <= 0 {
		req.Size = ptr.Of(int64(10))
	}

	resp, err := openapiauthApp.OpenAuthApplication.ListPersonalAccessTokens(ctx, &req)
	if err != nil {
		logs.CtxErrorf(ctx, "OpenAuthApplication.ListPersonalAccessTokens failed, err=%v", err)
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// CreatePersonalAccessTokenAndPermission .
// @router /api/permission_api/pat/create_personal_access_token_and_permission [POST]
func CreatePersonalAccessTokenAndPermission(ctx context.Context, c *app.RequestContext) {
	var err error
	var req openapiauth.CreatePersonalAccessTokenAndPermissionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if err = checkCPATParams(ctx, &req); err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := openapiauthApp.OpenAuthApplication.CreatePersonalAccessToken(ctx, &req)
	if err != nil {
		logs.CtxErrorf(ctx, "OpenAuthApplicationService.CreatePersonalAccessToken failed, err=%v", err)
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// checkCPATParams 检查创建个人访问令牌的参数
func checkCPATParams(ctx context.Context, req *openapiauth.CreatePersonalAccessTokenAndPermissionRequest) error {

	if req.Name == "" {
		return errorx.New(errno.ErrPermissionInvalidParamCode, errorx.KV("msg", "name is required"))
	}
	return nil
}

// UpdatePersonalAccessTokenAndPermission .
// @router /api/permission_api/pat/update_personal_access_token_and_permission [POST]
func UpdatePersonalAccessTokenAndPermission(ctx context.Context, c *app.RequestContext) {
	var err error
	var req openapiauth.UpdatePersonalAccessTokenAndPermissionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := openapiauthApp.OpenAuthApplication.UpdatePersonalAccessTokenAndPermission(ctx, &req)
	if err != nil {
		logs.CtxErrorf(ctx, "OpenAuthApplication.UpdatePersonalAccessTokenAndPermission failed, err=%v", err)
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}
