/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	appApplication "github.com/coze-dev/coze-studio/backend/application/app"

	resource "github.com/coze-dev/coze-studio/backend/api/model/resource"
	"github.com/coze-dev/coze-studio/backend/application/search"
)

// LibraryResourceList .
// @router /api/plugin_api/library_resource_list [POST]
func LibraryResourceList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req resource.LibraryResourceListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "space_id is invalid")
		return
	}
	if req.GetSize() > 100 {
		invalidParamRequestResponse(c, "size is too large")
		return
	}

	resp, err := search.SearchSVC.LibraryResourceList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ProjectResourceList .
// @router /api/plugin_api/project_resource_list [POST]
func ProjectResourceList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req resource.ProjectResourceListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "space_id is invalid")
		return
	}
	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "project_id is invalid")
		return
	}

	resp, err := search.SearchSVC.ProjectResourceList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ResourceCopyDispatch .
// @router /api/plugin_api/resource_copy_dispatch [POST]
func ResourceCopyDispatch(ctx context.Context, c *app.RequestContext) {
	var err error
	var req resource.ResourceCopyDispatchRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ResID <= 0 {
		invalidParamRequestResponse(c, "res_id is invalid")
		return
	}
	if req.ResType <= 0 {
		invalidParamRequestResponse(c, "res_type is invalid")
		return
	}
	if req.GetProjectID() <= 0 {
		invalidParamRequestResponse(c, "project_id is invalid")
		return
	}

	resp, err := appApplication.APPApplicationSVC.ResourceCopyDispatch(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ResourceCopyDetail .
// @router /api/plugin_api/resource_copy_detail [POST]
func ResourceCopyDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req resource.ResourceCopyDetailRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.TaskID == "" {
		invalidParamRequestResponse(c, "task_id is invalid")
		return
	}

	resp, err := appApplication.APPApplicationSVC.ResourceCopyDetail(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ResourceCopyRetry .
// @router /api/plugin_api/resource_copy_retry [POST]
func ResourceCopyRetry(ctx context.Context, c *app.RequestContext) {
	var err error
	var req resource.ResourceCopyRetryRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp := new(resource.ResourceCopyRetryResponse)

	c.JSON(consts.StatusOK, resp)
}

// ResourceCopyCancel .
// @router /api/plugin_api/resource_copy_cancel [POST]
func ResourceCopyCancel(ctx context.Context, c *app.RequestContext) {
	var err error
	var req resource.ResourceCopyCancelRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(resource.ResourceCopyCancelResponse)

	c.JSON(consts.StatusOK, resp)
}
