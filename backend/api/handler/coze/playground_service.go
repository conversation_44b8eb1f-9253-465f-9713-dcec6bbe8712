/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/ocean/cloud/playground"
	appApplication "github.com/coze-dev/coze-studio/backend/application/app"
	"github.com/coze-dev/coze-studio/backend/application/prompt"
	"github.com/coze-dev/coze-studio/backend/application/shortcutcmd"
	"github.com/coze-dev/coze-studio/backend/application/singleagent"
	"github.com/coze-dev/coze-studio/backend/application/upload"
	"github.com/coze-dev/coze-studio/backend/application/user"
)

// UpdateDraftBotInfoAgw .
// @router /api/playground_api/draftbot/update_draft_bot_info [POST]
func UpdateDraftBotInfoAgw(ctx context.Context, c *app.RequestContext) {
	var req playground.UpdateDraftBotInfoAgwRequest
	err := c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.BotInfo == nil {
		invalidParamRequestResponse(c, "bot info is nil")
		return
	}

	if req.BotInfo.BotId == nil {
		invalidParamRequestResponse(c, "bot id is nil")
		return
	}

	resp, err := singleagent.SingleAgentSVC.UpdateSingleAgentDraft(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDraftBotInfoAgw .
// @router /api/playground_api/draftbot/get_draft_bot_info [POST]
func GetDraftBotInfoAgw(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetDraftBotInfoAgwRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.BotID == 0 {
		invalidParamRequestResponse(c, "bot id is nil")
		return
	}

	resp, err := singleagent.SingleAgentSVC.GetAgentBotInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetOfficialPromptResourceList .
// @router /api/playground_api/get_official_prompt_list [POST]
func GetOfficialPromptResourceList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetOfficialPromptResourceListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := prompt.PromptSVC.GetOfficialPromptResourceList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetPromptResourceInfo .
// @router /api/playground_api/get_prompt_resource_info [GET]
func GetPromptResourceInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetPromptResourceInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := prompt.PromptSVC.GetPromptResourceInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpsertPromptResource .
// @router /api/playground_api/upsert_prompt_resource [POST]
func UpsertPromptResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.UpsertPromptResourceRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.Prompt == nil {
		invalidParamRequestResponse(c, "prompt is nil")
		return
	}

	if req.Prompt.GetSpaceID() <= 0 {
		invalidParamRequestResponse(c, "space id is invalid")
		return
	}

	if len(req.Prompt.GetName()) <= 0 {
		invalidParamRequestResponse(c, "name is empty")
		return
	}

	resp, err := prompt.PromptSVC.UpsertPromptResource(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DeletePromptResource .
// @router /api/playground_api/delete_prompt_resource [POST]
func DeletePromptResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.DeletePromptResourceRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := prompt.PromptSVC.DeletePromptResource(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetSpaceListV2 .
// @router /api/playground_api/space/list [POST]
func GetSpaceListV2(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetSpaceListV2Request
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := user.UserApplicationSVC.GetSpaceListV2(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetImagexShortUrl .
// @router /api/playground_api/get_imagex_url [POST]
func GetImagexShortUrl(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetImagexShortUrlRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if len(req.Uris) == 0 {
		invalidParamRequestResponse(c, "uris is empty")
		return
	}

	resp, err := singleagent.SingleAgentSVC.GetImagexShortUrl(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// MGetUserBasicInfo .
// @router /api/playground_api/mget_user_info [POST]
func MGetUserBasicInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.MGetUserBasicInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := user.UserApplicationSVC.MGetUserBasicInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetBotPopupInfo .
// @router /api/playground_api/operate/get_bot_popup_info [POST]
func GetBotPopupInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetBotPopupInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if len(req.BotPopupTypes) == 0 {
		invalidParamRequestResponse(c, "bot popup types is empty")
		return
	}

	resp, err := singleagent.SingleAgentSVC.GetAgentPopupInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateBotPopupInfo .
// @router /api/playground_api/operate/update_bot_popup_info [POST]
func UpdateBotPopupInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.UpdateBotPopupInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := singleagent.SingleAgentSVC.UpdateAgentPopupInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// CreateUpdateShortcutCommand .
// @router /api/playground_api/create_update_shortcut_command [POST]
func CreateUpdateShortcutCommand(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.CreateUpdateShortcutCommandRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	shortCuts, err := shortcutcmd.ShortcutCmdSVC.Handler(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	resp := new(playground.CreateUpdateShortcutCommandResponse)
	resp.Shortcuts = shortCuts
	resp.Code = 0
	resp.Msg = ""
	c.JSON(consts.StatusOK, resp)
}

// ReportUserBehavior .
// @router /api/playground_api/report_user_behavior [POST]
func ReportUserBehavior(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.ReportUserBehaviorRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ResourceID <= 0 {
		invalidParamRequestResponse(c, "resource id is invalid")
		return
	}

	resp := new(playground.ReportUserBehaviorResponse)

	if req.ResourceType == playground.SpaceResourceType_DraftBot {
		resp, err = singleagent.SingleAgentSVC.ReportUserBehavior(ctx, &req)
		if err != nil {
			internalServerErrorResponse(ctx, c, err)
			return
		}
	} else if req.ResourceType == playground.SpaceResourceType_Project {
		resp, err = appApplication.APPApplicationSVC.ReportUserBehavior(ctx, &req)
		if err != nil {
			internalServerErrorResponse(ctx, c, err)
			return
		}
	}

	c.JSON(consts.StatusOK, resp)
}

// GetFileUrls .
// @router /api/playground_api/get_file_list [POST]
func GetFileUrls(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetFileUrlsRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	iconList, err := upload.SVC.GetShortcutIcons(ctx)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	resp := new(playground.GetFileUrlsResponse)
	resp.FileList = iconList
	resp.Code = 0

	c.JSON(consts.StatusOK, resp)
}

// UploadFileOpen .
// @router /v1/files/upload [POST]
func UploadFileOpen(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.UploadFileOpenRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(playground.UploadFileOpenResponse)
	resp, err = upload.SVC.UploadFileOpen(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// GetBotOnlineInfo .
// @router /v1/bot/get_online_info [GET]
func GetBotOnlineInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req playground.GetBotOnlineInfoReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := singleagent.SingleAgentSVC.GetAgentOnlineInfo(ctx, &req)

	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}
