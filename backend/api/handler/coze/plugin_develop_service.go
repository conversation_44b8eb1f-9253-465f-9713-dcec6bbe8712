/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"
	"regexp"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/ocean/cloud/plugin_develop"
	common "github.com/coze-dev/coze-studio/backend/api/model/plugin_develop_common"
	"github.com/coze-dev/coze-studio/backend/application/plugin"
	appworkflow "github.com/coze-dev/coze-studio/backend/application/workflow"
)

// GetPlaygroundPluginList .
// @router /api/plugin_api/get_playground_plugin_list [POST]
func GetPlaygroundPluginList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetPlaygroundPluginListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.GetSpaceID() <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.GetPage() <= 0 {
		invalidParamRequestResponse(c, "page is invalid")
		return
	}
	if req.GetSize() >= 30 {
		invalidParamRequestResponse(c, "size is invalid")
		return
	}

	// when there is only one element in the types list, and the element type is workflow, use workflow service
	// TODO Figure out when there are multiple values for types
	if len(req.GetPluginTypes()) == 1 && req.GetPluginTypes()[0] == int32(common.PluginType_WORKFLOW) {
		resp, err := appworkflow.SVC.GetPlaygroundPluginList(ctx, &req)
		if err != nil {
			internalServerErrorResponse(ctx, c, err)
			return
		}
		c.JSON(consts.StatusOK, resp)
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetPlaygroundPluginList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// RegisterPluginMeta .
// @router /api/plugin_api/register_plugin_meta [POST]
func RegisterPluginMeta(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.RegisterPluginMetaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.GetName() == "" {
		invalidParamRequestResponse(c, "plugin name is invalid")
		return
	}
	if req.GetDesc() == "" {
		invalidParamRequestResponse(c, "plugin desc is invalid")
		return
	}
	if req.URL != nil && (*req.URL == "" || len(*req.URL) > 512) {
		invalidParamRequestResponse(c, "plugin url is invalid")
		return
	}
	if req.Icon == nil || req.Icon.URI == "" || len(req.Icon.URI) > 512 {
		invalidParamRequestResponse(c, "plugin icon is invalid")
		return
	}
	if req.AuthType == nil {
		invalidParamRequestResponse(c, "plugin auth type is invalid")
		return
	}
	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.ProjectID != nil {
		if *req.ProjectID <= 0 {
			invalidParamRequestResponse(c, "projectID is invalid")
			return
		}
	}
	if req.GetPluginType() != common.PluginType_PLUGIN {
		invalidParamRequestResponse(c, "plugin type is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.RegisterPluginMeta(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetPluginAPIs .
// @router /api/plugin_api/get_plugin_apis [POST]
func GetPluginAPIs(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetPluginAPIsRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if len(req.APIIds) == 0 {
		if req.Page <= 0 {
			invalidParamRequestResponse(c, "page is invalid")
			return
		}
		if req.Size >= 30 {
			invalidParamRequestResponse(c, "size is invalid")
			return
		}
	}

	resp, err := plugin.PluginApplicationSVC.GetPluginAPIs(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetPluginInfo .
// @router /api/plugin_api/get_plugin_info [POST]
func GetPluginInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetPluginInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetPluginInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetUpdatedAPIs .
// @router /api/plugin_api/get_updated_apis [POST]
func GetUpdatedAPIs(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetUpdatedAPIsRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetUpdatedAPIs(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetOAuthStatus .
// @router /api/plugin_api/get_oauth_status [POST]
func GetOAuthStatus(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetOAuthStatusRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetOAuthStatus(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// CheckAndLockPluginEdit .
// @router /api/plugin_api/check_and_lock_plugin_edit [POST]
func CheckAndLockPluginEdit(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.CheckAndLockPluginEditRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.CheckAndLockPluginEdit(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdatePlugin .
// @router /api/plugin_api/update [POST]
func UpdatePlugin(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.UpdatePluginRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.AiPlugin == "" {
		invalidParamRequestResponse(c, "plugin manifest is invalid")
		return
	}
	if req.Openapi == "" {
		invalidParamRequestResponse(c, "plugin openapi doc is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.UpdatePlugin(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DeleteAPI .
// @router /api/plugin_api/delete_api [POST]
func DeleteAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.DeleteAPIRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.APIID <= 0 {
		invalidParamRequestResponse(c, "apiID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.DeleteAPI(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DelPlugin .
// @router /api/plugin_api/del_plugin [POST]
func DelPlugin(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.DelPluginRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.DelPlugin(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// PublishPlugin .
// @router /api/plugin_api/publish_plugin [POST]
func PublishPlugin(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.PublishPluginRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.VersionName == "" || len(req.VersionName) > 255 {
		invalidParamRequestResponse(c, "version name is invalid")
		return
	}

	match, _ := regexp.MatchString(`^v\d+\.\d+\.\d+$`, req.VersionName)
	if !match {
		invalidParamRequestResponse(c, "version name is invalid")
		return
	}

	if req.VersionDesc == "" {
		invalidParamRequestResponse(c, "version desc is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.PublishPlugin(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdatePluginMeta .
// @router /api/plugin_api/update_plugin_meta [POST]
func UpdatePluginMeta(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.UpdatePluginMetaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.Name != nil && *req.Name == "" {
		invalidParamRequestResponse(c, "plugin name is invalid")
		return
	}
	if req.Desc != nil && *req.Desc == "" {
		invalidParamRequestResponse(c, "plugin desc is invalid")
		return
	}
	if req.URL != nil && (*req.URL == "" || len(*req.URL) > 512) {
		invalidParamRequestResponse(c, "plugin server url is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.UpdatePluginMeta(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetBotDefaultParams .
// @router /api/plugin_api/get_bot_default_params [POST]
func GetBotDefaultParams(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetBotDefaultParamsRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.BotID <= 0 {
		invalidParamRequestResponse(c, "botID is invalid")
		return
	}
	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.APIName == "" {
		invalidParamRequestResponse(c, "apiName is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetBotDefaultParams(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateBotDefaultParams .
// @router /api/plugin_api/update_bot_default_params [POST]
func UpdateBotDefaultParams(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.UpdateBotDefaultParamsRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.BotID <= 0 {
		invalidParamRequestResponse(c, "botID is invalid")
		return
	}
	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.APIName == "" {
		invalidParamRequestResponse(c, "apiName is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.UpdateBotDefaultParams(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// CreateAPI .
// @router /api/plugin_api/create_api [POST]
func CreateAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.CreateAPIRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.Name == "" || len(req.Name) > 255 {
		invalidParamRequestResponse(c, "api name is invalid")
		return
	}
	if req.Desc == "" {
		invalidParamRequestResponse(c, "api desc is invalid")
		return
	}
	if req.Path != nil && (*req.Path == "" || len(*req.Path) > 512) {
		invalidParamRequestResponse(c, "api path is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.CreateAPI(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateAPI .
// @router /api/plugin_api/update_api [POST]
func UpdateAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.UpdateAPIRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.APIID <= 0 {
		invalidParamRequestResponse(c, "apiID is invalid")
		return
	}
	if req.Name != nil && (*req.Name == "" || len(*req.Name) > 255) {
		invalidParamRequestResponse(c, "api name is invalid")
		return
	}
	if req.Desc != nil && (*req.Desc == "" || len(*req.Desc) > 255) {
		invalidParamRequestResponse(c, "api desc is invalid")
		return
	}
	if req.Path != nil && (*req.Path == "" || len(*req.Path) > 512) {
		invalidParamRequestResponse(c, "api path is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.UpdateAPI(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetUserAuthority .
// @router /api/plugin_api/get_user_authority [POST]
func GetUserAuthority(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetUserAuthorityRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetUserAuthority(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DebugAPI .
// @router /api/plugin_api/debug_api [POST]
func DebugAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.DebugAPIRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.APIID <= 0 {
		invalidParamRequestResponse(c, "apiID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.DebugAPI(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UnlockPluginEdit .
// @router /api/plugin_api/unlock_plugin_edit [POST]
func UnlockPluginEdit(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.UnlockPluginEditRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := plugin.PluginApplicationSVC.UnlockPluginEdit(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetPluginNextVersion .
// @router /api/plugin_api/get_plugin_next_version [POST]
func GetPluginNextVersion(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetPluginNextVersionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetPluginNextVersion(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// RegisterPlugin .
// @router /api/developer/register [POST]
func RegisterPlugin(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.RegisterPluginRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.GetSpaceID() <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.ProjectID != nil && *req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "projectID is invalid")
		return
	}
	if req.AiPlugin == "" {
		invalidParamRequestResponse(c, "plugin manifest is invalid")
		return
	}
	if req.Openapi == "" {
		invalidParamRequestResponse(c, "plugin openapi doc is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.RegisterPlugin(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDevPluginList .
// @router /api/plugin_api/get_dev_plugin_list [POST]
func GetDevPluginList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetDevPluginListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "projectID is invalid")
		return
	}
	if req.GetPage() <= 0 {
		invalidParamRequestResponse(c, "page is invalid")
		return
	}
	if req.GetSize() <= 0 {
		invalidParamRequestResponse(c, "size is invalid")
		return
	}
	if req.GetSize() > 50 {
		invalidParamRequestResponse(c, "size is too large")
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetDevPluginList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// Convert2OpenAPI .
// @router /api/plugin_api/convert_to_openapi [POST]
func Convert2OpenAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.Convert2OpenAPIRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.Data == "" {
		invalidParamRequestResponse(c, "data is invalid")
		return
	}
	if req.PluginURL != nil && *req.PluginURL == "" {
		invalidParamRequestResponse(c, "pluginURL is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.Convert2OpenAPI(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetOAuthSchemaAPI .
// @router /api/plugin_api/get_oauth_schema [POST]
func GetOAuthSchemaAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetOAuthSchemaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetOAuthSchema(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetOAuthSchema .
// @router /api/plugin/get_oauth_schema [POST]
func GetOAuthSchema(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetOAuthSchemaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetOAuthSchema(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// BatchCreateAPI .
// @router /api/plugin_api/batch_create_api [POST]
func BatchCreateAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.BatchCreateAPIRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "spaceID is invalid")
		return
	}
	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}
	if req.AiPlugin == "" {
		invalidParamRequestResponse(c, "plugin manifest is invalid")
		return
	}
	if req.Openapi == "" {
		invalidParamRequestResponse(c, "plugin openapi doc is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.BatchCreateAPI(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// RevokeAuthToken .
// @router /api/plugin_api/revoke_auth_token [POST]
func RevokeAuthToken(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.RevokeAuthTokenRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.PluginID <= 0 {
		invalidParamRequestResponse(c, "pluginID is invalid")
		return
	}

	resp, err := plugin.PluginApplicationSVC.RevokeAuthToken(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetQueriedOAuthPluginList .
// @router /api/plugin_api/get_queried_oauth_plugins [POST]
func GetQueriedOAuthPluginList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req plugin_develop.GetQueriedOAuthPluginListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.BotID <= 0 {
		invalidParamRequestResponse(c, "entityID is required")
		return
	}

	resp, err := plugin.PluginApplicationSVC.GetQueriedOAuthPluginList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}
