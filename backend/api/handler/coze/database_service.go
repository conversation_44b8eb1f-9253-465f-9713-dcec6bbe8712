/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/knowledge/document"
	"github.com/coze-dev/coze-studio/backend/api/model/table"
	"github.com/coze-dev/coze-studio/backend/application/memory"
	"github.com/coze-dev/coze-studio/backend/application/singleagent"
)

// ListDatabase .
// @router /api/memory/database/list [POST]
func ListDatabase(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.ListDatabaseRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.ListDatabase(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDatabaseByID .
// @router /api/memory/database/get_by_id [POST]
func GetDatabaseByID(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.SingleDatabaseRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.GetDatabaseByID(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// AddDatabase .
// @router /api/memory/database/add [POST]
func AddDatabase(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.AddDatabaseRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.AddDatabase(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateDatabase .
// @router /api/memory/database/update [POST]
func UpdateDatabase(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.UpdateDatabaseRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.UpdateDatabase(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DeleteDatabase .
// @router /api/memory/database/delete [POST]
func DeleteDatabase(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.DeleteDatabaseRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.DeleteDatabase(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// BindDatabase .
// @router /api/memory/database/bind_to_bot [POST]
func BindDatabase(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.BindDatabaseToBotRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := singleagent.SingleAgentSVC.BindDatabase(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UnBindDatabase .
// @router /api/memory/database/unbind_to_bot [POST]
func UnBindDatabase(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.BindDatabaseToBotRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := singleagent.SingleAgentSVC.UnBindDatabase(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ListDatabaseRecords .
// @router /api/memory/database/list_records [POST]
func ListDatabaseRecords(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.ListDatabaseRecordsRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.ListDatabaseRecords(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateDatabaseRecords .
// @router /api/memory/database/update_records [POST]
func UpdateDatabaseRecords(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.UpdateDatabaseRecordsRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.UpdateDatabaseRecords(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetOnlineDatabaseId .
// @router /api/memory/database/get_online_database_id [POST]
func GetOnlineDatabaseId(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.GetOnlineDatabaseIdRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.GetOnlineDatabaseId(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ResetBotTable .
// @router /api/memory/database/table/reset [POST]
func ResetBotTable(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.ResetBotTableRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.ResetBotTable(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDatabaseTemplate .
// @router /api/memory/database/get_template [POST]
func GetDatabaseTemplate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.GetDatabaseTemplateRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.GetDatabaseTemplate(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetConnectorName .
// @router /api/memory/database/get_connector_name [POST]
func GetConnectorName(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.GetSpaceConnectorListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.GetConnectorName(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetBotDatabase .
// @router /api/memory/database/table/list_new [POST]
func GetBotDatabase(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.GetBotTableRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.GetBotDatabase(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateDatabaseBotSwitch .
// @router /api/memory/database/update_bot_switch [POST]
func UpdateDatabaseBotSwitch(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.UpdateDatabaseBotSwitchRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := singleagent.SingleAgentSVC.UpdatePromptDisable(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDatabaseTableSchema .
// @router /api/memory/table_schema/get [POST]
func GetDatabaseTableSchema(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.GetTableSchemaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	var resp *document.GetTableSchemaInfoResponse
	resp, err = memory.DatabaseApplicationSVC.GetDatabaseTableSchema(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// SubmitDatabaseInsertTask .
// @router /api/memory/table_file/submit [POST]
func SubmitDatabaseInsertTask(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.SubmitDatabaseInsertRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.SubmitDatabaseInsertTask(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DatabaseFileProgressData .
// @router /api/memory/table_file/get_progress [POST]
func DatabaseFileProgressData(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.GetDatabaseFileProgressRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.DatabaseFileProgressData(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ValidateDatabaseTableSchema .
// @router /api/memory/table_schema/validate [POST]
func ValidateDatabaseTableSchema(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.ValidateTableSchemaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := memory.DatabaseApplicationSVC.ValidateDatabaseTableSchema(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}
