// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package document

import (
	"github.com/coze-dev/coze-studio/backend/api/model/base"
	"github.com/coze-dev/coze-studio/backend/api/model/common"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

// 获取表格结构、预览数据
type GetDocumentTableInfoRequest struct {
	// 如果为第一次本地文件上传的表格，传递该值
	TosURI *string `thrift:"tos_uri,2,optional" form:"tos_uri" json:"tos_uri,omitempty" query:"tos_uri"`
	// 如果为已有 document 的表格，传递该值
	DocumentID *int64 `thrift:"document_id,3,optional" form:"document_id" json:"document_id,string,omitempty"`
	// 创建人[http接口不需要传递]
	CreatorID int64      `thrift:"creator_id,4" form:"creator_id" json:"creator_id" query:"creator_id"`
	Base      *base.Base `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetDocumentTableInfoRequest() *GetDocumentTableInfoRequest {
	return &GetDocumentTableInfoRequest{}
}

func (p *GetDocumentTableInfoRequest) InitDefault() {
}

var GetDocumentTableInfoRequest_TosURI_DEFAULT string

func (p *GetDocumentTableInfoRequest) GetTosURI() (v string) {
	if !p.IsSetTosURI() {
		return GetDocumentTableInfoRequest_TosURI_DEFAULT
	}
	return *p.TosURI
}

var GetDocumentTableInfoRequest_DocumentID_DEFAULT int64

func (p *GetDocumentTableInfoRequest) GetDocumentID() (v int64) {
	if !p.IsSetDocumentID() {
		return GetDocumentTableInfoRequest_DocumentID_DEFAULT
	}
	return *p.DocumentID
}

func (p *GetDocumentTableInfoRequest) GetCreatorID() (v int64) {
	return p.CreatorID
}

var GetDocumentTableInfoRequest_Base_DEFAULT *base.Base

func (p *GetDocumentTableInfoRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetDocumentTableInfoRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetDocumentTableInfoRequest = map[int16]string{
	2:   "tos_uri",
	3:   "document_id",
	4:   "creator_id",
	255: "Base",
}

func (p *GetDocumentTableInfoRequest) IsSetTosURI() bool {
	return p.TosURI != nil
}

func (p *GetDocumentTableInfoRequest) IsSetDocumentID() bool {
	return p.DocumentID != nil
}

func (p *GetDocumentTableInfoRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetDocumentTableInfoRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDocumentTableInfoRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetDocumentTableInfoRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TosURI = _field
	return nil
}
func (p *GetDocumentTableInfoRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DocumentID = _field
	return nil
}
func (p *GetDocumentTableInfoRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorID = _field
	return nil
}
func (p *GetDocumentTableInfoRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetDocumentTableInfoRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDocumentTableInfoRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDocumentTableInfoRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTosURI() {
		if err = oprot.WriteFieldBegin("tos_uri", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TosURI); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetDocumentTableInfoRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDocumentID() {
		if err = oprot.WriteFieldBegin("document_id", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.DocumentID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetDocumentTableInfoRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_id", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreatorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetDocumentTableInfoRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetDocumentTableInfoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDocumentTableInfoRequest(%+v)", *p)

}

type GetDocumentTableInfoResponse struct {
	Code      int32                   `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg       string                  `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	SheetList []*common.DocTableSheet `thrift:"sheet_list,3" form:"sheet_list" json:"sheet_list" query:"sheet_list"`
	// key: sheet_id -> list<common.DocTableColumn>
	TableMeta map[string][]*common.DocTableColumn `thrift:"table_meta,4" form:"table_meta" json:"table_meta"`
	// key: sheet_id -> list_preview_data
	PreviewData map[string][]map[string]string `thrift:"preview_data,5" form:"preview_data" json:"preview_data"`
	BaseResp    *base.BaseResp                 `thrift:"BaseResp,255,required" form:"-" json:"-" query:"-"`
}

func NewGetDocumentTableInfoResponse() *GetDocumentTableInfoResponse {
	return &GetDocumentTableInfoResponse{}
}

func (p *GetDocumentTableInfoResponse) InitDefault() {
}

func (p *GetDocumentTableInfoResponse) GetCode() (v int32) {
	return p.Code
}

func (p *GetDocumentTableInfoResponse) GetMsg() (v string) {
	return p.Msg
}

func (p *GetDocumentTableInfoResponse) GetSheetList() (v []*common.DocTableSheet) {
	return p.SheetList
}

func (p *GetDocumentTableInfoResponse) GetTableMeta() (v map[string][]*common.DocTableColumn) {
	return p.TableMeta
}

func (p *GetDocumentTableInfoResponse) GetPreviewData() (v map[string][]map[string]string) {
	return p.PreviewData
}

var GetDocumentTableInfoResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetDocumentTableInfoResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetDocumentTableInfoResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetDocumentTableInfoResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	3:   "sheet_list",
	4:   "table_meta",
	5:   "preview_data",
	255: "BaseResp",
}

func (p *GetDocumentTableInfoResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetDocumentTableInfoResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDocumentTableInfoResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetDocumentTableInfoResponse[fieldId]))
}

func (p *GetDocumentTableInfoResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetDocumentTableInfoResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetDocumentTableInfoResponse) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.DocTableSheet, 0, size)
	values := make([]common.DocTableSheet, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SheetList = _field
	return nil
}
func (p *GetDocumentTableInfoResponse) ReadField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string][]*common.DocTableColumn, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_val := make([]*common.DocTableColumn, 0, size)
		values := make([]common.DocTableColumn, size)
		for i := 0; i < size; i++ {
			_elem := &values[i]
			_elem.InitDefault()

			if err := _elem.Read(iprot); err != nil {
				return err
			}

			_val = append(_val, _elem)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.TableMeta = _field
	return nil
}
func (p *GetDocumentTableInfoResponse) ReadField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string][]map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_val := make([]map[string]string, 0, size)
		for i := 0; i < size; i++ {
			_, _, size, err := iprot.ReadMapBegin()
			if err != nil {
				return err
			}
			_elem := make(map[string]string, size)
			for i := 0; i < size; i++ {
				var _key1 string
				if v, err := iprot.ReadString(); err != nil {
					return err
				} else {
					_key1 = v
				}

				var _val1 string
				if v, err := iprot.ReadString(); err != nil {
					return err
				} else {
					_val1 = v
				}

				_elem[_key1] = _val1
			}
			if err := iprot.ReadMapEnd(); err != nil {
				return err
			}

			_val = append(_val, _elem)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.PreviewData = _field
	return nil
}
func (p *GetDocumentTableInfoResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetDocumentTableInfoResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDocumentTableInfoResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDocumentTableInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetDocumentTableInfoResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetDocumentTableInfoResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sheet_list", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SheetList)); err != nil {
		return err
	}
	for _, v := range p.SheetList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetDocumentTableInfoResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("table_meta", thrift.MAP, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.TableMeta)); err != nil {
		return err
	}
	for k, v := range p.TableMeta {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
			return err
		}
		for _, v := range v {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetDocumentTableInfoResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("preview_data", thrift.MAP, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.PreviewData)); err != nil {
		return err
	}
	for k, v := range p.PreviewData {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteListBegin(thrift.MAP, len(v)); err != nil {
			return err
		}
		for _, v := range v {
			if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(v)); err != nil {
				return err
			}
			for k, v := range v {
				if err := oprot.WriteString(k); err != nil {
					return err
				}
				if err := oprot.WriteString(v); err != nil {
					return err
				}
			}
			if err := oprot.WriteMapEnd(); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetDocumentTableInfoResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetDocumentTableInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDocumentTableInfoResponse(%+v)", *p)

}

type GetTableSchemaInfoResponse struct {
	Code      int32                   `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg       string                  `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	SheetList []*common.DocTableSheet `thrift:"sheet_list,3" form:"sheet_list" json:"sheet_list" query:"sheet_list"`
	// 选中的 sheet 的 schema, 不选择默认返回第一个 sheet
	TableMeta []*common.DocTableColumn `thrift:"table_meta,4" form:"table_meta" json:"table_meta" query:"table_meta"`
	// knowledge table 场景中会返回
	PreviewData []map[int64]string `thrift:"preview_data,5" form:"preview_data" json:"preview_data" query:"preview_data"`
	BaseResp    *base.BaseResp     `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetTableSchemaInfoResponse() *GetTableSchemaInfoResponse {
	return &GetTableSchemaInfoResponse{}
}

func (p *GetTableSchemaInfoResponse) InitDefault() {
}

func (p *GetTableSchemaInfoResponse) GetCode() (v int32) {
	return p.Code
}

func (p *GetTableSchemaInfoResponse) GetMsg() (v string) {
	return p.Msg
}

func (p *GetTableSchemaInfoResponse) GetSheetList() (v []*common.DocTableSheet) {
	return p.SheetList
}

func (p *GetTableSchemaInfoResponse) GetTableMeta() (v []*common.DocTableColumn) {
	return p.TableMeta
}

func (p *GetTableSchemaInfoResponse) GetPreviewData() (v []map[int64]string) {
	return p.PreviewData
}

var GetTableSchemaInfoResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetTableSchemaInfoResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetTableSchemaInfoResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetTableSchemaInfoResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	3:   "sheet_list",
	4:   "table_meta",
	5:   "preview_data",
	255: "BaseResp",
}

func (p *GetTableSchemaInfoResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetTableSchemaInfoResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetTableSchemaInfoResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetTableSchemaInfoResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetTableSchemaInfoResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetTableSchemaInfoResponse) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.DocTableSheet, 0, size)
	values := make([]common.DocTableSheet, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SheetList = _field
	return nil
}
func (p *GetTableSchemaInfoResponse) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.DocTableColumn, 0, size)
	values := make([]common.DocTableColumn, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TableMeta = _field
	return nil
}
func (p *GetTableSchemaInfoResponse) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]map[int64]string, 0, size)
	for i := 0; i < size; i++ {
		_, _, size, err := iprot.ReadMapBegin()
		if err != nil {
			return err
		}
		_elem := make(map[int64]string, size)
		for i := 0; i < size; i++ {
			var _key int64
			if v, err := iprot.ReadI64(); err != nil {
				return err
			} else {
				_key = v
			}

			var _val string
			if v, err := iprot.ReadString(); err != nil {
				return err
			} else {
				_val = v
			}

			_elem[_key] = _val
		}
		if err := iprot.ReadMapEnd(); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PreviewData = _field
	return nil
}
func (p *GetTableSchemaInfoResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetTableSchemaInfoResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetTableSchemaInfoResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetTableSchemaInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetTableSchemaInfoResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetTableSchemaInfoResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sheet_list", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SheetList)); err != nil {
		return err
	}
	for _, v := range p.SheetList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetTableSchemaInfoResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("table_meta", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TableMeta)); err != nil {
		return err
	}
	for _, v := range p.TableMeta {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetTableSchemaInfoResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("preview_data", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.MAP, len(p.PreviewData)); err != nil {
		return err
	}
	for _, v := range p.PreviewData {
		if err := oprot.WriteMapBegin(thrift.I64, thrift.STRING, len(v)); err != nil {
			return err
		}
		for k, v := range v {
			if err := oprot.WriteI64(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetTableSchemaInfoResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetTableSchemaInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTableSchemaInfoResponse(%+v)", *p)

}
