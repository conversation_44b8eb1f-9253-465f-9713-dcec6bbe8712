// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package product_common

import (
	"github.com/coze-dev/coze-studio/backend/api/model/flow/marketplace/marketplace_common"
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type ProductEntityType int64

const (
	ProductEntityType_Bot    ProductEntityType = 1
	ProductEntityType_Plugin ProductEntityType = 2
	// Workflow = 3 ,
	ProductEntityType_SocialScene ProductEntityType = 4
	ProductEntityType_Project     ProductEntityType = 6
	// 历史工作流，后续不会再有（废弃）
	ProductEntityType_WorkflowTemplate ProductEntityType = 13
	// 历史图像流模板，后续不会再有（废弃）
	ProductEntityType_ImageflowTemplate ProductEntityType = 15
	// 模板通用标识，仅用于绑定模板相关的配置，不绑定商品
	ProductEntityType_TemplateCommon ProductEntityType = 20
	// Bot 模板
	ProductEntityType_BotTemplate ProductEntityType = 21
	// 工作流模板
	ProductEntityType_WorkflowTemplateV2 ProductEntityType = 23
	// 图像流模板（该类型已下线，合并入 workflow，但历史数据会保留，前端视作 workflow 展示）
	ProductEntityType_ImageflowTemplateV2 ProductEntityType = 25
	// 项目模板
	ProductEntityType_ProjectTemplate ProductEntityType = 26
	// coze token 类商品，理论上只会有一个
	ProductEntityType_CozeToken ProductEntityType = 50
	// 订阅 credit 的流量包，理论上只会有一个
	ProductEntityType_MsgCredit ProductEntityType = 55
	// 消息订阅类商品，理论上只有一个
	ProductEntityType_SubsMsgCredit ProductEntityType = 60
	ProductEntityType_Common        ProductEntityType = 99
	// 专题（兼容之前的设计）
	ProductEntityType_Topic ProductEntityType = 101
)

func (p ProductEntityType) String() string {
	switch p {
	case ProductEntityType_Bot:
		return "Bot"
	case ProductEntityType_Plugin:
		return "Plugin"
	case ProductEntityType_SocialScene:
		return "SocialScene"
	case ProductEntityType_Project:
		return "Project"
	case ProductEntityType_WorkflowTemplate:
		return "WorkflowTemplate"
	case ProductEntityType_ImageflowTemplate:
		return "ImageflowTemplate"
	case ProductEntityType_TemplateCommon:
		return "TemplateCommon"
	case ProductEntityType_BotTemplate:
		return "BotTemplate"
	case ProductEntityType_WorkflowTemplateV2:
		return "WorkflowTemplateV2"
	case ProductEntityType_ImageflowTemplateV2:
		return "ImageflowTemplateV2"
	case ProductEntityType_ProjectTemplate:
		return "ProjectTemplate"
	case ProductEntityType_CozeToken:
		return "CozeToken"
	case ProductEntityType_MsgCredit:
		return "MsgCredit"
	case ProductEntityType_SubsMsgCredit:
		return "SubsMsgCredit"
	case ProductEntityType_Common:
		return "Common"
	case ProductEntityType_Topic:
		return "Topic"
	}
	return "<UNSET>"
}

func ProductEntityTypeFromString(s string) (ProductEntityType, error) {
	switch s {
	case "Bot":
		return ProductEntityType_Bot, nil
	case "Plugin":
		return ProductEntityType_Plugin, nil
	case "SocialScene":
		return ProductEntityType_SocialScene, nil
	case "Project":
		return ProductEntityType_Project, nil
	case "WorkflowTemplate":
		return ProductEntityType_WorkflowTemplate, nil
	case "ImageflowTemplate":
		return ProductEntityType_ImageflowTemplate, nil
	case "TemplateCommon":
		return ProductEntityType_TemplateCommon, nil
	case "BotTemplate":
		return ProductEntityType_BotTemplate, nil
	case "WorkflowTemplateV2":
		return ProductEntityType_WorkflowTemplateV2, nil
	case "ImageflowTemplateV2":
		return ProductEntityType_ImageflowTemplateV2, nil
	case "ProjectTemplate":
		return ProductEntityType_ProjectTemplate, nil
	case "CozeToken":
		return ProductEntityType_CozeToken, nil
	case "MsgCredit":
		return ProductEntityType_MsgCredit, nil
	case "SubsMsgCredit":
		return ProductEntityType_SubsMsgCredit, nil
	case "Common":
		return ProductEntityType_Common, nil
	case "Topic":
		return ProductEntityType_Topic, nil
	}
	return ProductEntityType(0), fmt.Errorf("not a valid ProductEntityType string")
}

func ProductEntityTypePtr(v ProductEntityType) *ProductEntityType { return &v }
func (p *ProductEntityType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductEntityType(result.Int64)
	return
}

func (p *ProductEntityType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SortType int64

const (
	SortType_Heat   SortType = 1
	SortType_Newest SortType = 2
	// 收藏时间
	SortType_FavoriteTime SortType = 3
	// 相关性，只用于搜索场景
	SortType_Relative SortType = 4
)

func (p SortType) String() string {
	switch p {
	case SortType_Heat:
		return "Heat"
	case SortType_Newest:
		return "Newest"
	case SortType_FavoriteTime:
		return "FavoriteTime"
	case SortType_Relative:
		return "Relative"
	}
	return "<UNSET>"
}

func SortTypeFromString(s string) (SortType, error) {
	switch s {
	case "Heat":
		return SortType_Heat, nil
	case "Newest":
		return SortType_Newest, nil
	case "FavoriteTime":
		return SortType_FavoriteTime, nil
	case "Relative":
		return SortType_Relative, nil
	}
	return SortType(0), fmt.Errorf("not a valid SortType string")
}

func SortTypePtr(v SortType) *SortType { return &v }
func (p *SortType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SortType(result.Int64)
	return
}

func (p *SortType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProductPublishMode int64

const (
	ProductPublishMode_OpenSource   ProductPublishMode = 1
	ProductPublishMode_ClosedSource ProductPublishMode = 2
)

func (p ProductPublishMode) String() string {
	switch p {
	case ProductPublishMode_OpenSource:
		return "OpenSource"
	case ProductPublishMode_ClosedSource:
		return "ClosedSource"
	}
	return "<UNSET>"
}

func ProductPublishModeFromString(s string) (ProductPublishMode, error) {
	switch s {
	case "OpenSource":
		return ProductPublishMode_OpenSource, nil
	case "ClosedSource":
		return ProductPublishMode_ClosedSource, nil
	}
	return ProductPublishMode(0), fmt.Errorf("not a valid ProductPublishMode string")
}

func ProductPublishModePtr(v ProductPublishMode) *ProductPublishMode { return &v }
func (p *ProductPublishMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductPublishMode(result.Int64)
	return
}

func (p *ProductPublishMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProductListSource int64

const (
	// 推荐列表页
	ProductListSource_Recommend ProductListSource = 1
	// 个性化推荐
	ProductListSource_CustomizedRecommend ProductListSource = 2
)

func (p ProductListSource) String() string {
	switch p {
	case ProductListSource_Recommend:
		return "Recommend"
	case ProductListSource_CustomizedRecommend:
		return "CustomizedRecommend"
	}
	return "<UNSET>"
}

func ProductListSourceFromString(s string) (ProductListSource, error) {
	switch s {
	case "Recommend":
		return ProductListSource_Recommend, nil
	case "CustomizedRecommend":
		return ProductListSource_CustomizedRecommend, nil
	}
	return ProductListSource(0), fmt.Errorf("not a valid ProductListSource string")
}

func ProductListSourcePtr(v ProductListSource) *ProductListSource { return &v }
func (p *ProductListSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductListSource(result.Int64)
	return
}

func (p *ProductListSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginType int64

const (
	// default
	PluginType_CLoudPlugin PluginType = 0
	PluginType_LocalPlugin PluginType = 1
)

func (p PluginType) String() string {
	switch p {
	case PluginType_CLoudPlugin:
		return "CLoudPlugin"
	case PluginType_LocalPlugin:
		return "LocalPlugin"
	}
	return "<UNSET>"
}

func PluginTypeFromString(s string) (PluginType, error) {
	switch s {
	case "CLoudPlugin":
		return PluginType_CLoudPlugin, nil
	case "LocalPlugin":
		return PluginType_LocalPlugin, nil
	}
	return PluginType(0), fmt.Errorf("not a valid PluginType string")
}

func PluginTypePtr(v PluginType) *PluginType { return &v }
func (p *PluginType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginType(result.Int64)
	return
}

func (p *PluginType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProductPaidType int64

const (
	ProductPaidType_Free ProductPaidType = 0
	ProductPaidType_Paid ProductPaidType = 1
)

func (p ProductPaidType) String() string {
	switch p {
	case ProductPaidType_Free:
		return "Free"
	case ProductPaidType_Paid:
		return "Paid"
	}
	return "<UNSET>"
}

func ProductPaidTypeFromString(s string) (ProductPaidType, error) {
	switch s {
	case "Free":
		return ProductPaidType_Free, nil
	case "Paid":
		return ProductPaidType_Paid, nil
	}
	return ProductPaidType(0), fmt.Errorf("not a valid ProductPaidType string")
}

func ProductPaidTypePtr(v ProductPaidType) *ProductPaidType { return &v }
func (p *ProductPaidType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductPaidType(result.Int64)
	return
}

func (p *ProductPaidType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProductStatus int64

const (
	// 从未上架
	ProductStatus_NeverListed ProductStatus = 0
	ProductStatus_Listed      ProductStatus = 1
	ProductStatus_Unlisted    ProductStatus = 2
	ProductStatus_Banned      ProductStatus = 3
)

func (p ProductStatus) String() string {
	switch p {
	case ProductStatus_NeverListed:
		return "NeverListed"
	case ProductStatus_Listed:
		return "Listed"
	case ProductStatus_Unlisted:
		return "Unlisted"
	case ProductStatus_Banned:
		return "Banned"
	}
	return "<UNSET>"
}

func ProductStatusFromString(s string) (ProductStatus, error) {
	switch s {
	case "NeverListed":
		return ProductStatus_NeverListed, nil
	case "Listed":
		return ProductStatus_Listed, nil
	case "Unlisted":
		return ProductStatus_Unlisted, nil
	case "Banned":
		return ProductStatus_Banned, nil
	}
	return ProductStatus(0), fmt.Errorf("not a valid ProductStatus string")
}

func ProductStatusPtr(v ProductStatus) *ProductStatus { return &v }
func (p *ProductStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductStatus(result.Int64)
	return
}

func (p *ProductStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProductDraftStatus int64

const (
	// 默认
	ProductDraftStatus_Default ProductDraftStatus = 0
	// 审核中
	ProductDraftStatus_Pending ProductDraftStatus = 1
	// 审核通过
	ProductDraftStatus_Approved ProductDraftStatus = 2
	// 审核不通过
	ProductDraftStatus_Rejected ProductDraftStatus = 3
	// 已废弃
	ProductDraftStatus_Abandoned ProductDraftStatus = 4
)

func (p ProductDraftStatus) String() string {
	switch p {
	case ProductDraftStatus_Default:
		return "Default"
	case ProductDraftStatus_Pending:
		return "Pending"
	case ProductDraftStatus_Approved:
		return "Approved"
	case ProductDraftStatus_Rejected:
		return "Rejected"
	case ProductDraftStatus_Abandoned:
		return "Abandoned"
	}
	return "<UNSET>"
}

func ProductDraftStatusFromString(s string) (ProductDraftStatus, error) {
	switch s {
	case "Default":
		return ProductDraftStatus_Default, nil
	case "Pending":
		return ProductDraftStatus_Pending, nil
	case "Approved":
		return ProductDraftStatus_Approved, nil
	case "Rejected":
		return ProductDraftStatus_Rejected, nil
	case "Abandoned":
		return ProductDraftStatus_Abandoned, nil
	}
	return ProductDraftStatus(0), fmt.Errorf("not a valid ProductDraftStatus string")
}

func ProductDraftStatusPtr(v ProductDraftStatus) *ProductDraftStatus { return &v }
func (p *ProductDraftStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductDraftStatus(result.Int64)
	return
}

func (p *ProductDraftStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type InputType int64

const (
	InputType_String  InputType = 1
	InputType_Integer InputType = 2
	InputType_Boolean InputType = 3
	InputType_Double  InputType = 4
	InputType_List    InputType = 5
	InputType_Object  InputType = 6
)

func (p InputType) String() string {
	switch p {
	case InputType_String:
		return "String"
	case InputType_Integer:
		return "Integer"
	case InputType_Boolean:
		return "Boolean"
	case InputType_Double:
		return "Double"
	case InputType_List:
		return "List"
	case InputType_Object:
		return "Object"
	}
	return "<UNSET>"
}

func InputTypeFromString(s string) (InputType, error) {
	switch s {
	case "String":
		return InputType_String, nil
	case "Integer":
		return InputType_Integer, nil
	case "Boolean":
		return InputType_Boolean, nil
	case "Double":
		return InputType_Double, nil
	case "List":
		return InputType_List, nil
	case "Object":
		return InputType_Object, nil
	}
	return InputType(0), fmt.Errorf("not a valid InputType string")
}

func InputTypePtr(v InputType) *InputType { return &v }
func (p *InputType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = InputType(result.Int64)
	return
}

func (p *InputType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginParamTypeFormat int64

const (
	PluginParamTypeFormat_ImageUrl PluginParamTypeFormat = 1
)

func (p PluginParamTypeFormat) String() string {
	switch p {
	case PluginParamTypeFormat_ImageUrl:
		return "ImageUrl"
	}
	return "<UNSET>"
}

func PluginParamTypeFormatFromString(s string) (PluginParamTypeFormat, error) {
	switch s {
	case "ImageUrl":
		return PluginParamTypeFormat_ImageUrl, nil
	}
	return PluginParamTypeFormat(0), fmt.Errorf("not a valid PluginParamTypeFormat string")
}

func PluginParamTypeFormatPtr(v PluginParamTypeFormat) *PluginParamTypeFormat { return &v }
func (p *PluginParamTypeFormat) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginParamTypeFormat(result.Int64)
	return
}

func (p *PluginParamTypeFormat) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type WorkflowNodeType int64

const (
	// 开始
	WorkflowNodeType_Start WorkflowNodeType = 1
	// 结束
	WorkflowNodeType_End WorkflowNodeType = 2
	// 大模型
	WorkflowNodeType_LLM WorkflowNodeType = 3
	// 插件
	WorkflowNodeType_Api WorkflowNodeType = 4
	// 代码
	WorkflowNodeType_Code WorkflowNodeType = 5
	// 知识库
	WorkflowNodeType_Dataset WorkflowNodeType = 6
	// 选择器
	WorkflowNodeType_If WorkflowNodeType = 8
	// 工作流
	WorkflowNodeType_SubWorkflow WorkflowNodeType = 9
	// 变量
	WorkflowNodeType_Variable WorkflowNodeType = 11
	// 数据库
	WorkflowNodeType_Database WorkflowNodeType = 12
	// 消息
	WorkflowNodeType_Message WorkflowNodeType = 13
)

func (p WorkflowNodeType) String() string {
	switch p {
	case WorkflowNodeType_Start:
		return "Start"
	case WorkflowNodeType_End:
		return "End"
	case WorkflowNodeType_LLM:
		return "LLM"
	case WorkflowNodeType_Api:
		return "Api"
	case WorkflowNodeType_Code:
		return "Code"
	case WorkflowNodeType_Dataset:
		return "Dataset"
	case WorkflowNodeType_If:
		return "If"
	case WorkflowNodeType_SubWorkflow:
		return "SubWorkflow"
	case WorkflowNodeType_Variable:
		return "Variable"
	case WorkflowNodeType_Database:
		return "Database"
	case WorkflowNodeType_Message:
		return "Message"
	}
	return "<UNSET>"
}

func WorkflowNodeTypeFromString(s string) (WorkflowNodeType, error) {
	switch s {
	case "Start":
		return WorkflowNodeType_Start, nil
	case "End":
		return WorkflowNodeType_End, nil
	case "LLM":
		return WorkflowNodeType_LLM, nil
	case "Api":
		return WorkflowNodeType_Api, nil
	case "Code":
		return WorkflowNodeType_Code, nil
	case "Dataset":
		return WorkflowNodeType_Dataset, nil
	case "If":
		return WorkflowNodeType_If, nil
	case "SubWorkflow":
		return WorkflowNodeType_SubWorkflow, nil
	case "Variable":
		return WorkflowNodeType_Variable, nil
	case "Database":
		return WorkflowNodeType_Database, nil
	case "Message":
		return WorkflowNodeType_Message, nil
	}
	return WorkflowNodeType(0), fmt.Errorf("not a valid WorkflowNodeType string")
}

func WorkflowNodeTypePtr(v WorkflowNodeType) *WorkflowNodeType { return &v }
func (p *WorkflowNodeType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = WorkflowNodeType(result.Int64)
	return
}

func (p *WorkflowNodeType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SocialSceneRoleType int64

const (
	SocialSceneRoleType_Host      SocialSceneRoleType = 1
	SocialSceneRoleType_PresetBot SocialSceneRoleType = 2
	SocialSceneRoleType_Custom    SocialSceneRoleType = 3
)

func (p SocialSceneRoleType) String() string {
	switch p {
	case SocialSceneRoleType_Host:
		return "Host"
	case SocialSceneRoleType_PresetBot:
		return "PresetBot"
	case SocialSceneRoleType_Custom:
		return "Custom"
	}
	return "<UNSET>"
}

func SocialSceneRoleTypeFromString(s string) (SocialSceneRoleType, error) {
	switch s {
	case "Host":
		return SocialSceneRoleType_Host, nil
	case "PresetBot":
		return SocialSceneRoleType_PresetBot, nil
	case "Custom":
		return SocialSceneRoleType_Custom, nil
	}
	return SocialSceneRoleType(0), fmt.Errorf("not a valid SocialSceneRoleType string")
}

func SocialSceneRoleTypePtr(v SocialSceneRoleType) *SocialSceneRoleType { return &v }
func (p *SocialSceneRoleType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SocialSceneRoleType(result.Int64)
	return
}

func (p *SocialSceneRoleType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type UIPreviewType int64

const (
	// UI 预览类型，定义对齐 UI Builder，目前用于 Project
	UIPreviewType_Web UIPreviewType = 1
	// 移动端
	UIPreviewType_Client UIPreviewType = 2
)

func (p UIPreviewType) String() string {
	switch p {
	case UIPreviewType_Web:
		return "Web"
	case UIPreviewType_Client:
		return "Client"
	}
	return "<UNSET>"
}

func UIPreviewTypeFromString(s string) (UIPreviewType, error) {
	switch s {
	case "Web":
		return UIPreviewType_Web, nil
	case "Client":
		return UIPreviewType_Client, nil
	}
	return UIPreviewType(0), fmt.Errorf("not a valid UIPreviewType string")
}

func UIPreviewTypePtr(v UIPreviewType) *UIPreviewType { return &v }
func (p *UIPreviewType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = UIPreviewType(result.Int64)
	return
}

func (p *UIPreviewType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type FavoriteListSource int64

const (
	// 用户自己创建的
	FavoriteListSource_CreatedByMe FavoriteListSource = 1
)

func (p FavoriteListSource) String() string {
	switch p {
	case FavoriteListSource_CreatedByMe:
		return "CreatedByMe"
	}
	return "<UNSET>"
}

func FavoriteListSourceFromString(s string) (FavoriteListSource, error) {
	switch s {
	case "CreatedByMe":
		return FavoriteListSource_CreatedByMe, nil
	}
	return FavoriteListSource(0), fmt.Errorf("not a valid FavoriteListSource string")
}

func FavoriteListSourcePtr(v FavoriteListSource) *FavoriteListSource { return &v }
func (p *FavoriteListSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FavoriteListSource(result.Int64)
	return
}

func (p *FavoriteListSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AuditStatus = ProductDraftStatus

type CommercialSetting struct {
	CommercialType ProductPaidType `thrift:"commercial_type,1,required" form:"commercial_type,required" json:"commercial_type,required"`
}

func NewCommercialSetting() *CommercialSetting {
	return &CommercialSetting{}
}

func (p *CommercialSetting) InitDefault() {
}

func (p *CommercialSetting) GetCommercialType() (v ProductPaidType) {
	return p.CommercialType
}

var fieldIDToName_CommercialSetting = map[int16]string{
	1: "commercial_type",
}

func (p *CommercialSetting) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCommercialType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCommercialType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCommercialType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommercialSetting[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CommercialSetting[fieldId]))
}

func (p *CommercialSetting) ReadField1(iprot thrift.TProtocol) error {

	var _field ProductPaidType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProductPaidType(v)
	}
	p.CommercialType = _field
	return nil
}

func (p *CommercialSetting) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CommercialSetting"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CommercialSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("commercial_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CommercialType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CommercialSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommercialSetting(%+v)", *p)

}

type UserLabel struct {
	LabelID   string `thrift:"label_id,1" form:"label_id" json:"label_id"`
	LabelName string `thrift:"label_name,2" form:"label_name" json:"label_name"`
	IconURI   string `thrift:"icon_uri,3" form:"icon_uri" json:"icon_uri"`
	IconURL   string `thrift:"icon_url,4" form:"icon_url" json:"icon_url"`
	JumpLink  string `thrift:"jump_link,5" form:"jump_link" json:"jump_link"`
}

func NewUserLabel() *UserLabel {
	return &UserLabel{}
}

func (p *UserLabel) InitDefault() {
}

func (p *UserLabel) GetLabelID() (v string) {
	return p.LabelID
}

func (p *UserLabel) GetLabelName() (v string) {
	return p.LabelName
}

func (p *UserLabel) GetIconURI() (v string) {
	return p.IconURI
}

func (p *UserLabel) GetIconURL() (v string) {
	return p.IconURL
}

func (p *UserLabel) GetJumpLink() (v string) {
	return p.JumpLink
}

var fieldIDToName_UserLabel = map[int16]string{
	1: "label_id",
	2: "label_name",
	3: "icon_uri",
	4: "icon_url",
	5: "jump_link",
}

func (p *UserLabel) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserLabel[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UserLabel) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LabelID = _field
	return nil
}
func (p *UserLabel) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LabelName = _field
	return nil
}
func (p *UserLabel) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURI = _field
	return nil
}
func (p *UserLabel) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURL = _field
	return nil
}
func (p *UserLabel) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.JumpLink = _field
	return nil
}

func (p *UserLabel) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserLabel"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserLabel) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("label_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LabelID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UserLabel) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("label_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LabelName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UserLabel) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_uri", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UserLabel) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_url", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UserLabel) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("jump_link", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.JumpLink); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UserLabel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserLabel(%+v)", *p)

}

type UserInfo struct {
	UserID     int64                          `thrift:"user_id,1" form:"user_id" json:"user_id,string"`
	UserName   string                         `thrift:"user_name,2" form:"user_name" json:"user_name"`
	Name       string                         `thrift:"name,3" form:"name" json:"name"`
	AvatarURL  string                         `thrift:"avatar_url,4" form:"avatar_url" json:"avatar_url"`
	UserLabel  *UserLabel                     `thrift:"user_label,5,optional" form:"user_label" json:"user_label,omitempty"`
	FollowType *marketplace_common.FollowType `thrift:"follow_type,6,optional" form:"follow_type" json:"follow_type,omitempty"`
}

func NewUserInfo() *UserInfo {
	return &UserInfo{}
}

func (p *UserInfo) InitDefault() {
}

func (p *UserInfo) GetUserID() (v int64) {
	return p.UserID
}

func (p *UserInfo) GetUserName() (v string) {
	return p.UserName
}

func (p *UserInfo) GetName() (v string) {
	return p.Name
}

func (p *UserInfo) GetAvatarURL() (v string) {
	return p.AvatarURL
}

var UserInfo_UserLabel_DEFAULT *UserLabel

func (p *UserInfo) GetUserLabel() (v *UserLabel) {
	if !p.IsSetUserLabel() {
		return UserInfo_UserLabel_DEFAULT
	}
	return p.UserLabel
}

var UserInfo_FollowType_DEFAULT marketplace_common.FollowType

func (p *UserInfo) GetFollowType() (v marketplace_common.FollowType) {
	if !p.IsSetFollowType() {
		return UserInfo_FollowType_DEFAULT
	}
	return *p.FollowType
}

var fieldIDToName_UserInfo = map[int16]string{
	1: "user_id",
	2: "user_name",
	3: "name",
	4: "avatar_url",
	5: "user_label",
	6: "follow_type",
}

func (p *UserInfo) IsSetUserLabel() bool {
	return p.UserLabel != nil
}

func (p *UserInfo) IsSetFollowType() bool {
	return p.FollowType != nil
}

func (p *UserInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UserInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *UserInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserName = _field
	return nil
}
func (p *UserInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *UserInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvatarURL = _field
	return nil
}
func (p *UserInfo) ReadField5(iprot thrift.TProtocol) error {
	_field := NewUserLabel()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.UserLabel = _field
	return nil
}
func (p *UserInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *marketplace_common.FollowType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := marketplace_common.FollowType(v)
		_field = &tmp
	}
	p.FollowType = _field
	return nil
}

func (p *UserInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UserInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UserInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UserInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("avatar_url", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AvatarURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UserInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserLabel() {
		if err = oprot.WriteFieldBegin("user_label", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.UserLabel.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *UserInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFollowType() {
		if err = oprot.WriteFieldBegin("follow_type", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.FollowType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *UserInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserInfo(%+v)", *p)

}

type ImageInfo struct {
	URI string `thrift:"uri,1" form:"uri" json:"uri"`
	URL string `thrift:"url,2" form:"url" json:"url"`
}

func NewImageInfo() *ImageInfo {
	return &ImageInfo{}
}

func (p *ImageInfo) InitDefault() {
}

func (p *ImageInfo) GetURI() (v string) {
	return p.URI
}

func (p *ImageInfo) GetURL() (v string) {
	return p.URL
}

var fieldIDToName_ImageInfo = map[int16]string{
	1: "uri",
	2: "url",
}

func (p *ImageInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ImageInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ImageInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URI = _field
	return nil
}
func (p *ImageInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}

func (p *ImageInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ImageInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ImageInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uri", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ImageInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("url", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ImageInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImageInfo(%+v)", *p)

}

type OpeningDialog struct {
	// Bot开场白
	Content string `thrift:"content,1" form:"content" json:"content"`
}

func NewOpeningDialog() *OpeningDialog {
	return &OpeningDialog{}
}

func (p *OpeningDialog) InitDefault() {
}

func (p *OpeningDialog) GetContent() (v string) {
	return p.Content
}

var fieldIDToName_OpeningDialog = map[int16]string{
	1: "content",
}

func (p *OpeningDialog) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpeningDialog[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpeningDialog) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}

func (p *OpeningDialog) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("OpeningDialog"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpeningDialog) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OpeningDialog) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpeningDialog(%+v)", *p)

}

type ChargeSKUExtra struct {
	Quantity     int64 `thrift:"Quantity,1" form:"quantity" json:"quantity,string"`
	IsSelfDefine bool  `thrift:"IsSelfDefine,2" form:"is_self_define" json:"is_self_define"`
}

func NewChargeSKUExtra() *ChargeSKUExtra {
	return &ChargeSKUExtra{}
}

func (p *ChargeSKUExtra) InitDefault() {
}

func (p *ChargeSKUExtra) GetQuantity() (v int64) {
	return p.Quantity
}

func (p *ChargeSKUExtra) GetIsSelfDefine() (v bool) {
	return p.IsSelfDefine
}

var fieldIDToName_ChargeSKUExtra = map[int16]string{
	1: "Quantity",
	2: "IsSelfDefine",
}

func (p *ChargeSKUExtra) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeSKUExtra[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChargeSKUExtra) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Quantity = _field
	return nil
}
func (p *ChargeSKUExtra) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsSelfDefine = _field
	return nil
}

func (p *ChargeSKUExtra) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeSKUExtra"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeSKUExtra) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Quantity", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Quantity); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChargeSKUExtra) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsSelfDefine", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsSelfDefine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChargeSKUExtra) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeSKUExtra(%+v)", *p)

}

type FavoriteEntity struct {
	EntityID    int64             `thrift:"EntityID,1" form:"entity_id" json:"entity_id,string"`
	EntityType  ProductEntityType `thrift:"EntityType,2" form:"entity_type" json:"entity_type"`
	Name        string            `thrift:"Name,4" form:"name" json:"name"`
	IconURL     string            `thrift:"IconURL,5" form:"icon_url" json:"icon_url"`
	Description string            `thrift:"Description,6" form:"description" json:"description"`
	// 废弃，使用UserInfo
	Seller *SellerInfo `thrift:"Seller,7" form:"seller" json:"seller"`
	// 用于跳转到Bot编辑页
	SpaceID int64 `thrift:"SpaceID,8" form:"space_id" json:"space_id,string"`
	// 用户是否有该实体所在Space的权限
	HasSpacePermission bool `thrift:"HasSpacePermission,9" form:"has_space_permission" json:"has_space_permission"`
	// 收藏时间
	FavoriteAt   int64                 `thrift:"FavoriteAt,10" form:"favorite_at" json:"favorite_at,string"`
	ProductExtra *FavoriteProductExtra `thrift:"ProductExtra,11,optional" form:"product_extra" json:"product_extra,omitempty"`
	UserInfo     *UserInfo             `thrift:"UserInfo,12" form:"user_info" json:"user_info"`
	PluginExtra  *FavoritePluginExtra  `thrift:"PluginExtra,13,optional" form:"plugin_extra" json:"plugin_extra,omitempty"`
}

func NewFavoriteEntity() *FavoriteEntity {
	return &FavoriteEntity{}
}

func (p *FavoriteEntity) InitDefault() {
}

func (p *FavoriteEntity) GetEntityID() (v int64) {
	return p.EntityID
}

func (p *FavoriteEntity) GetEntityType() (v ProductEntityType) {
	return p.EntityType
}

func (p *FavoriteEntity) GetName() (v string) {
	return p.Name
}

func (p *FavoriteEntity) GetIconURL() (v string) {
	return p.IconURL
}

func (p *FavoriteEntity) GetDescription() (v string) {
	return p.Description
}

var FavoriteEntity_Seller_DEFAULT *SellerInfo

func (p *FavoriteEntity) GetSeller() (v *SellerInfo) {
	if !p.IsSetSeller() {
		return FavoriteEntity_Seller_DEFAULT
	}
	return p.Seller
}

func (p *FavoriteEntity) GetSpaceID() (v int64) {
	return p.SpaceID
}

func (p *FavoriteEntity) GetHasSpacePermission() (v bool) {
	return p.HasSpacePermission
}

func (p *FavoriteEntity) GetFavoriteAt() (v int64) {
	return p.FavoriteAt
}

var FavoriteEntity_ProductExtra_DEFAULT *FavoriteProductExtra

func (p *FavoriteEntity) GetProductExtra() (v *FavoriteProductExtra) {
	if !p.IsSetProductExtra() {
		return FavoriteEntity_ProductExtra_DEFAULT
	}
	return p.ProductExtra
}

var FavoriteEntity_UserInfo_DEFAULT *UserInfo

func (p *FavoriteEntity) GetUserInfo() (v *UserInfo) {
	if !p.IsSetUserInfo() {
		return FavoriteEntity_UserInfo_DEFAULT
	}
	return p.UserInfo
}

var FavoriteEntity_PluginExtra_DEFAULT *FavoritePluginExtra

func (p *FavoriteEntity) GetPluginExtra() (v *FavoritePluginExtra) {
	if !p.IsSetPluginExtra() {
		return FavoriteEntity_PluginExtra_DEFAULT
	}
	return p.PluginExtra
}

var fieldIDToName_FavoriteEntity = map[int16]string{
	1:  "EntityID",
	2:  "EntityType",
	4:  "Name",
	5:  "IconURL",
	6:  "Description",
	7:  "Seller",
	8:  "SpaceID",
	9:  "HasSpacePermission",
	10: "FavoriteAt",
	11: "ProductExtra",
	12: "UserInfo",
	13: "PluginExtra",
}

func (p *FavoriteEntity) IsSetSeller() bool {
	return p.Seller != nil
}

func (p *FavoriteEntity) IsSetProductExtra() bool {
	return p.ProductExtra != nil
}

func (p *FavoriteEntity) IsSetUserInfo() bool {
	return p.UserInfo != nil
}

func (p *FavoriteEntity) IsSetPluginExtra() bool {
	return p.PluginExtra != nil
}

func (p *FavoriteEntity) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FavoriteEntity[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FavoriteEntity) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EntityID = _field
	return nil
}
func (p *FavoriteEntity) ReadField2(iprot thrift.TProtocol) error {

	var _field ProductEntityType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProductEntityType(v)
	}
	p.EntityType = _field
	return nil
}
func (p *FavoriteEntity) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *FavoriteEntity) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURL = _field
	return nil
}
func (p *FavoriteEntity) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *FavoriteEntity) ReadField7(iprot thrift.TProtocol) error {
	_field := NewSellerInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Seller = _field
	return nil
}
func (p *FavoriteEntity) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpaceID = _field
	return nil
}
func (p *FavoriteEntity) ReadField9(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasSpacePermission = _field
	return nil
}
func (p *FavoriteEntity) ReadField10(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FavoriteAt = _field
	return nil
}
func (p *FavoriteEntity) ReadField11(iprot thrift.TProtocol) error {
	_field := NewFavoriteProductExtra()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ProductExtra = _field
	return nil
}
func (p *FavoriteEntity) ReadField12(iprot thrift.TProtocol) error {
	_field := NewUserInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.UserInfo = _field
	return nil
}
func (p *FavoriteEntity) ReadField13(iprot thrift.TProtocol) error {
	_field := NewFavoritePluginExtra()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PluginExtra = _field
	return nil
}

func (p *FavoriteEntity) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FavoriteEntity"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FavoriteEntity) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EntityID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EntityID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FavoriteEntity) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EntityType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.EntityType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FavoriteEntity) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FavoriteEntity) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IconURL", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FavoriteEntity) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *FavoriteEntity) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Seller", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Seller.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *FavoriteEntity) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SpaceID", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SpaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *FavoriteEntity) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HasSpacePermission", thrift.BOOL, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasSpacePermission); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *FavoriteEntity) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FavoriteAt", thrift.I64, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.FavoriteAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *FavoriteEntity) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductExtra() {
		if err = oprot.WriteFieldBegin("ProductExtra", thrift.STRUCT, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ProductExtra.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *FavoriteEntity) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserInfo", thrift.STRUCT, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.UserInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *FavoriteEntity) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetPluginExtra() {
		if err = oprot.WriteFieldBegin("PluginExtra", thrift.STRUCT, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.PluginExtra.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *FavoriteEntity) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FavoriteEntity(%+v)", *p)

}

type SellerInfo struct {
	UserID    int64  `thrift:"UserID,1" form:"user_id" json:"user_id,string"`
	UserName  string `thrift:"UserName,2" form:"user_name" json:"user_name"`
	AvatarURL string `thrift:"AvatarURL,3" form:"avatar_url" json:"avatar_url"`
}

func NewSellerInfo() *SellerInfo {
	return &SellerInfo{}
}

func (p *SellerInfo) InitDefault() {
}

func (p *SellerInfo) GetUserID() (v int64) {
	return p.UserID
}

func (p *SellerInfo) GetUserName() (v string) {
	return p.UserName
}

func (p *SellerInfo) GetAvatarURL() (v string) {
	return p.AvatarURL
}

var fieldIDToName_SellerInfo = map[int16]string{
	1: "UserID",
	2: "UserName",
	3: "AvatarURL",
}

func (p *SellerInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SellerInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SellerInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *SellerInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserName = _field
	return nil
}
func (p *SellerInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvatarURL = _field
	return nil
}

func (p *SellerInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SellerInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SellerInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SellerInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SellerInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AvatarURL", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AvatarURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SellerInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SellerInfo(%+v)", *p)

}

type FavoriteProductExtra struct {
	ProductID     int64         `thrift:"ProductID,1" form:"product_id" json:"product_id,string"`
	ProductStatus ProductStatus `thrift:"ProductStatus,2" form:"product_status" json:"product_status"`
}

func NewFavoriteProductExtra() *FavoriteProductExtra {
	return &FavoriteProductExtra{}
}

func (p *FavoriteProductExtra) InitDefault() {
}

func (p *FavoriteProductExtra) GetProductID() (v int64) {
	return p.ProductID
}

func (p *FavoriteProductExtra) GetProductStatus() (v ProductStatus) {
	return p.ProductStatus
}

var fieldIDToName_FavoriteProductExtra = map[int16]string{
	1: "ProductID",
	2: "ProductStatus",
}

func (p *FavoriteProductExtra) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FavoriteProductExtra[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FavoriteProductExtra) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProductID = _field
	return nil
}
func (p *FavoriteProductExtra) ReadField2(iprot thrift.TProtocol) error {

	var _field ProductStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProductStatus(v)
	}
	p.ProductStatus = _field
	return nil
}

func (p *FavoriteProductExtra) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FavoriteProductExtra"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FavoriteProductExtra) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProductID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProductID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FavoriteProductExtra) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProductStatus", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ProductStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FavoriteProductExtra) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FavoriteProductExtra(%+v)", *p)

}

type FavoritePluginExtra struct {
	Tools []*PluginTool `thrift:"Tools,1" form:"tools" json:"tools"`
}

func NewFavoritePluginExtra() *FavoritePluginExtra {
	return &FavoritePluginExtra{}
}

func (p *FavoritePluginExtra) InitDefault() {
}

func (p *FavoritePluginExtra) GetTools() (v []*PluginTool) {
	return p.Tools
}

var fieldIDToName_FavoritePluginExtra = map[int16]string{
	1: "Tools",
}

func (p *FavoritePluginExtra) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FavoritePluginExtra[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FavoritePluginExtra) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginTool, 0, size)
	values := make([]PluginTool, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tools = _field
	return nil
}

func (p *FavoritePluginExtra) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FavoritePluginExtra"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FavoritePluginExtra) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tools", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tools)); err != nil {
		return err
	}
	for _, v := range p.Tools {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FavoritePluginExtra) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FavoritePluginExtra(%+v)", *p)

}

type PluginTool struct {
	ID          int64  `thrift:"ID,1" form:"id" json:"id,string"`
	Name        string `thrift:"Name,2" form:"name" json:"name"`
	Description string `thrift:"Description,3" form:"description" json:"description"`
}

func NewPluginTool() *PluginTool {
	return &PluginTool{}
}

func (p *PluginTool) InitDefault() {
}

func (p *PluginTool) GetID() (v int64) {
	return p.ID
}

func (p *PluginTool) GetName() (v string) {
	return p.Name
}

func (p *PluginTool) GetDescription() (v string) {
	return p.Description
}

var fieldIDToName_PluginTool = map[int16]string{
	1: "ID",
	2: "Name",
	3: "Description",
}

func (p *PluginTool) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginTool[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginTool) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *PluginTool) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PluginTool) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}

func (p *PluginTool) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginTool"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginTool) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginTool) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginTool) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PluginTool) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginTool(%+v)", *p)

}
