// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package intelligence

import (
	"github.com/coze-dev/coze-studio/backend/api/model/base"
	"github.com/coze-dev/coze-studio/backend/api/model/intelligence/common"
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type OrderBy int64

const (
	OrderBy_UpdateTime  OrderBy = 0
	OrderBy_CreateTime  OrderBy = 1
	OrderBy_PublishTime OrderBy = 2
)

func (p OrderBy) String() string {
	switch p {
	case OrderBy_UpdateTime:
		return "UpdateTime"
	case OrderBy_CreateTime:
		return "CreateTime"
	case OrderBy_PublishTime:
		return "PublishTime"
	}
	return "<UNSET>"
}

func OrderByFromString(s string) (OrderBy, error) {
	switch s {
	case "UpdateTime":
		return OrderBy_UpdateTime, nil
	case "CreateTime":
		return OrderBy_CreateTime, nil
	case "PublishTime":
		return OrderBy_PublishTime, nil
	}
	return OrderBy(0), fmt.<PERSON><PERSON><PERSON>("not a valid OrderBy string")
}

func OrderByPtr(v OrderBy) *OrderBy { return &v }
func (p *OrderBy) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OrderBy(result.Int64)
	return
}

func (p *OrderBy) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type OceanProjectOrderBy int64

const (
	OceanProjectOrderBy_UpdateTime OceanProjectOrderBy = 0
	OceanProjectOrderBy_CreateTime OceanProjectOrderBy = 1
)

func (p OceanProjectOrderBy) String() string {
	switch p {
	case OceanProjectOrderBy_UpdateTime:
		return "UpdateTime"
	case OceanProjectOrderBy_CreateTime:
		return "CreateTime"
	}
	return "<UNSET>"
}

func OceanProjectOrderByFromString(s string) (OceanProjectOrderBy, error) {
	switch s {
	case "UpdateTime":
		return OceanProjectOrderBy_UpdateTime, nil
	case "CreateTime":
		return OceanProjectOrderBy_CreateTime, nil
	}
	return OceanProjectOrderBy(0), fmt.Errorf("not a valid OceanProjectOrderBy string")
}

func OceanProjectOrderByPtr(v OceanProjectOrderBy) *OceanProjectOrderBy { return &v }
func (p *OceanProjectOrderBy) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OceanProjectOrderBy(result.Int64)
	return
}

func (p *OceanProjectOrderBy) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SearchScope int64

const (
	SearchScope_All        SearchScope = 0
	SearchScope_CreateByMe SearchScope = 1
)

func (p SearchScope) String() string {
	switch p {
	case SearchScope_All:
		return "All"
	case SearchScope_CreateByMe:
		return "CreateByMe"
	}
	return "<UNSET>"
}

func SearchScopeFromString(s string) (SearchScope, error) {
	switch s {
	case "All":
		return SearchScope_All, nil
	case "CreateByMe":
		return SearchScope_CreateByMe, nil
	}
	return SearchScope(0), fmt.Errorf("not a valid SearchScope string")
}

func SearchScopePtr(v SearchScope) *SearchScope { return &v }
func (p *SearchScope) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SearchScope(result.Int64)
	return
}

func (p *SearchScope) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type BotMode int64

const (
	BotMode_SingleMode   BotMode = 0
	BotMode_MultiMode    BotMode = 1
	BotMode_WorkflowMode BotMode = 2
)

func (p BotMode) String() string {
	switch p {
	case BotMode_SingleMode:
		return "SingleMode"
	case BotMode_MultiMode:
		return "MultiMode"
	case BotMode_WorkflowMode:
		return "WorkflowMode"
	}
	return "<UNSET>"
}

func BotModeFromString(s string) (BotMode, error) {
	switch s {
	case "SingleMode":
		return BotMode_SingleMode, nil
	case "MultiMode":
		return BotMode_MultiMode, nil
	case "WorkflowMode":
		return BotMode_WorkflowMode, nil
	}
	return BotMode(0), fmt.Errorf("not a valid BotMode string")
}

func BotModePtr(v BotMode) *BotMode { return &v }
func (p *BotMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = BotMode(result.Int64)
	return
}

func (p *BotMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type GetDraftIntelligenceListOption struct {
	// 是否需要个人版本Bot数据
	NeedReplica bool `thrift:"need_replica,1" form:"need_replica" json:"need_replica" query:"need_replica"`
}

func NewGetDraftIntelligenceListOption() *GetDraftIntelligenceListOption {
	return &GetDraftIntelligenceListOption{}
}

func (p *GetDraftIntelligenceListOption) InitDefault() {
}

func (p *GetDraftIntelligenceListOption) GetNeedReplica() (v bool) {
	return p.NeedReplica
}

var fieldIDToName_GetDraftIntelligenceListOption = map[int16]string{
	1: "need_replica",
}

func (p *GetDraftIntelligenceListOption) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDraftIntelligenceListOption[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetDraftIntelligenceListOption) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NeedReplica = _field
	return nil
}

func (p *GetDraftIntelligenceListOption) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDraftIntelligenceListOption"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDraftIntelligenceListOption) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("need_replica", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.NeedReplica); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetDraftIntelligenceListOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDraftIntelligenceListOption(%+v)", *p)

}

type GetDraftIntelligenceListRequest struct {
	SpaceID      int64                           `thrift:"space_id,1,required" form:"space_id,required" json:"space_id,string,required" query:"space_id,required"`
	Name         *string                         `thrift:"name,2,optional" form:"name" json:"name,omitempty" query:"name"`
	HasPublished *bool                           `thrift:"has_published,3,optional" form:"has_published" json:"has_published,omitempty" query:"has_published"`
	Status       []common.IntelligenceStatus     `thrift:"status,4,optional" form:"status" json:"status,omitempty" query:"status"`
	Types        []common.IntelligenceType       `thrift:"types,5,optional" form:"types" json:"types,omitempty" query:"types"`
	SearchScope  *SearchScope                    `thrift:"search_scope,6,optional" form:"search_scope" json:"search_scope,omitempty" query:"search_scope"`
	IsFav        *bool                           `thrift:"is_fav,51,optional" form:"is_fav" json:"is_fav,omitempty" query:"is_fav"`
	RecentlyOpen *bool                           `thrift:"recently_open,52,optional" form:"recently_open" json:"recently_open,omitempty" query:"recently_open"`
	Option       *GetDraftIntelligenceListOption `thrift:"option,99,optional" form:"option" json:"option,omitempty" query:"option"`
	OrderBy      *OrderBy                        `thrift:"order_by,100,optional" form:"order_by" json:"order_by,omitempty" query:"order_by"`
	CursorID     *string                         `thrift:"cursor_id,101,optional" form:"cursor_id" json:"cursor_id,omitempty" query:"cursor_id"`
	Size         *int32                          `thrift:"size,102,optional" form:"size" json:"size,omitempty" query:"size"`
	Base         *base.Base                      `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetDraftIntelligenceListRequest() *GetDraftIntelligenceListRequest {
	return &GetDraftIntelligenceListRequest{}
}

func (p *GetDraftIntelligenceListRequest) InitDefault() {
}

func (p *GetDraftIntelligenceListRequest) GetSpaceID() (v int64) {
	return p.SpaceID
}

var GetDraftIntelligenceListRequest_Name_DEFAULT string

func (p *GetDraftIntelligenceListRequest) GetName() (v string) {
	if !p.IsSetName() {
		return GetDraftIntelligenceListRequest_Name_DEFAULT
	}
	return *p.Name
}

var GetDraftIntelligenceListRequest_HasPublished_DEFAULT bool

func (p *GetDraftIntelligenceListRequest) GetHasPublished() (v bool) {
	if !p.IsSetHasPublished() {
		return GetDraftIntelligenceListRequest_HasPublished_DEFAULT
	}
	return *p.HasPublished
}

var GetDraftIntelligenceListRequest_Status_DEFAULT []common.IntelligenceStatus

func (p *GetDraftIntelligenceListRequest) GetStatus() (v []common.IntelligenceStatus) {
	if !p.IsSetStatus() {
		return GetDraftIntelligenceListRequest_Status_DEFAULT
	}
	return p.Status
}

var GetDraftIntelligenceListRequest_Types_DEFAULT []common.IntelligenceType

func (p *GetDraftIntelligenceListRequest) GetTypes() (v []common.IntelligenceType) {
	if !p.IsSetTypes() {
		return GetDraftIntelligenceListRequest_Types_DEFAULT
	}
	return p.Types
}

var GetDraftIntelligenceListRequest_SearchScope_DEFAULT SearchScope

func (p *GetDraftIntelligenceListRequest) GetSearchScope() (v SearchScope) {
	if !p.IsSetSearchScope() {
		return GetDraftIntelligenceListRequest_SearchScope_DEFAULT
	}
	return *p.SearchScope
}

var GetDraftIntelligenceListRequest_IsFav_DEFAULT bool

func (p *GetDraftIntelligenceListRequest) GetIsFav() (v bool) {
	if !p.IsSetIsFav() {
		return GetDraftIntelligenceListRequest_IsFav_DEFAULT
	}
	return *p.IsFav
}

var GetDraftIntelligenceListRequest_RecentlyOpen_DEFAULT bool

func (p *GetDraftIntelligenceListRequest) GetRecentlyOpen() (v bool) {
	if !p.IsSetRecentlyOpen() {
		return GetDraftIntelligenceListRequest_RecentlyOpen_DEFAULT
	}
	return *p.RecentlyOpen
}

var GetDraftIntelligenceListRequest_Option_DEFAULT *GetDraftIntelligenceListOption

func (p *GetDraftIntelligenceListRequest) GetOption() (v *GetDraftIntelligenceListOption) {
	if !p.IsSetOption() {
		return GetDraftIntelligenceListRequest_Option_DEFAULT
	}
	return p.Option
}

var GetDraftIntelligenceListRequest_OrderBy_DEFAULT OrderBy

func (p *GetDraftIntelligenceListRequest) GetOrderBy() (v OrderBy) {
	if !p.IsSetOrderBy() {
		return GetDraftIntelligenceListRequest_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var GetDraftIntelligenceListRequest_CursorID_DEFAULT string

func (p *GetDraftIntelligenceListRequest) GetCursorID() (v string) {
	if !p.IsSetCursorID() {
		return GetDraftIntelligenceListRequest_CursorID_DEFAULT
	}
	return *p.CursorID
}

var GetDraftIntelligenceListRequest_Size_DEFAULT int32

func (p *GetDraftIntelligenceListRequest) GetSize() (v int32) {
	if !p.IsSetSize() {
		return GetDraftIntelligenceListRequest_Size_DEFAULT
	}
	return *p.Size
}

var GetDraftIntelligenceListRequest_Base_DEFAULT *base.Base

func (p *GetDraftIntelligenceListRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetDraftIntelligenceListRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetDraftIntelligenceListRequest = map[int16]string{
	1:   "space_id",
	2:   "name",
	3:   "has_published",
	4:   "status",
	5:   "types",
	6:   "search_scope",
	51:  "is_fav",
	52:  "recently_open",
	99:  "option",
	100: "order_by",
	101: "cursor_id",
	102: "size",
	255: "Base",
}

func (p *GetDraftIntelligenceListRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetHasPublished() bool {
	return p.HasPublished != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetTypes() bool {
	return p.Types != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetSearchScope() bool {
	return p.SearchScope != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetIsFav() bool {
	return p.IsFav != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetRecentlyOpen() bool {
	return p.RecentlyOpen != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetOption() bool {
	return p.Option != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetCursorID() bool {
	return p.CursorID != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetSize() bool {
	return p.Size != nil
}

func (p *GetDraftIntelligenceListRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetDraftIntelligenceListRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSpaceID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSpaceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 51:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField51(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 52:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField52(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 99:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField99(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSpaceID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDraftIntelligenceListRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetDraftIntelligenceListRequest[fieldId]))
}

func (p *GetDraftIntelligenceListRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpaceID = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HasPublished = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]common.IntelligenceStatus, 0, size)
	for i := 0; i < size; i++ {

		var _elem common.IntelligenceStatus
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = common.IntelligenceStatus(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Status = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]common.IntelligenceType, 0, size)
	for i := 0; i < size; i++ {

		var _elem common.IntelligenceType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = common.IntelligenceType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Types = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *SearchScope
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SearchScope(v)
		_field = &tmp
	}
	p.SearchScope = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField51(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsFav = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField52(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RecentlyOpen = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField99(iprot thrift.TProtocol) error {
	_field := NewGetDraftIntelligenceListOption()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Option = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField100(iprot thrift.TProtocol) error {

	var _field *OrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderBy(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField101(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CursorID = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField102(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Size = _field
	return nil
}
func (p *GetDraftIntelligenceListRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetDraftIntelligenceListRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDraftIntelligenceListRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField51(oprot); err != nil {
			fieldId = 51
			goto WriteFieldError
		}
		if err = p.writeField52(oprot); err != nil {
			fieldId = 52
			goto WriteFieldError
		}
		if err = p.writeField99(oprot); err != nil {
			fieldId = 99
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDraftIntelligenceListRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("space_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SpaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetHasPublished() {
		if err = oprot.WriteFieldBegin("has_published", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.HasPublished); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err = oprot.WriteFieldBegin("status", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Status)); err != nil {
			return err
		}
		for _, v := range p.Status {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypes() {
		if err = oprot.WriteFieldBegin("types", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Types)); err != nil {
			return err
		}
		for _, v := range p.Types {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchScope() {
		if err = oprot.WriteFieldBegin("search_scope", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SearchScope)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField51(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsFav() {
		if err = oprot.WriteFieldBegin("is_fav", thrift.BOOL, 51); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsFav); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField52(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecentlyOpen() {
		if err = oprot.WriteFieldBegin("recently_open", thrift.BOOL, 52); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.RecentlyOpen); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 52 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 52 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField99(oprot thrift.TProtocol) (err error) {
	if p.IsSetOption() {
		if err = oprot.WriteFieldBegin("option", thrift.STRUCT, 99); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Option.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 99 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 99 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField100(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("order_by", thrift.I32, 100); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField101(oprot thrift.TProtocol) (err error) {
	if p.IsSetCursorID() {
		if err = oprot.WriteFieldBegin("cursor_id", thrift.STRING, 101); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CursorID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField102(oprot thrift.TProtocol) (err error) {
	if p.IsSetSize() {
		if err = oprot.WriteFieldBegin("size", thrift.I32, 102); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Size); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}
func (p *GetDraftIntelligenceListRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetDraftIntelligenceListRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDraftIntelligenceListRequest(%+v)", *p)

}

type IntelligencePublishInfo struct {
	PublishTime  string                  `thrift:"publish_time,1" form:"publish_time" json:"publish_time" query:"publish_time"`
	HasPublished bool                    `thrift:"has_published,2" form:"has_published" json:"has_published" query:"has_published"`
	Connectors   []*common.ConnectorInfo `thrift:"connectors,3" form:"connectors" json:"connectors" query:"connectors"`
}

func NewIntelligencePublishInfo() *IntelligencePublishInfo {
	return &IntelligencePublishInfo{}
}

func (p *IntelligencePublishInfo) InitDefault() {
}

func (p *IntelligencePublishInfo) GetPublishTime() (v string) {
	return p.PublishTime
}

func (p *IntelligencePublishInfo) GetHasPublished() (v bool) {
	return p.HasPublished
}

func (p *IntelligencePublishInfo) GetConnectors() (v []*common.ConnectorInfo) {
	return p.Connectors
}

var fieldIDToName_IntelligencePublishInfo = map[int16]string{
	1: "publish_time",
	2: "has_published",
	3: "connectors",
}

func (p *IntelligencePublishInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IntelligencePublishInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *IntelligencePublishInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PublishTime = _field
	return nil
}
func (p *IntelligencePublishInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasPublished = _field
	return nil
}
func (p *IntelligencePublishInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.ConnectorInfo, 0, size)
	values := make([]common.ConnectorInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Connectors = _field
	return nil
}

func (p *IntelligencePublishInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("IntelligencePublishInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IntelligencePublishInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_time", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PublishTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *IntelligencePublishInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("has_published", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasPublished); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *IntelligencePublishInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connectors", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Connectors)); err != nil {
		return err
	}
	for _, v := range p.Connectors {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *IntelligencePublishInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntelligencePublishInfo(%+v)", *p)

}

type IntelligencePermissionInfo struct {
	InCollaboration bool `thrift:"in_collaboration,1" form:"in_collaboration" json:"in_collaboration" query:"in_collaboration"`
	// 当前用户是否可删除
	CanDelete bool `thrift:"can_delete,2" form:"can_delete" json:"can_delete" query:"can_delete"`
	// 当前用户是否可查看，当前判断逻辑为用户是否在bot所在空间
	CanView bool `thrift:"can_view,3" form:"can_view" json:"can_view" query:"can_view"`
}

func NewIntelligencePermissionInfo() *IntelligencePermissionInfo {
	return &IntelligencePermissionInfo{}
}

func (p *IntelligencePermissionInfo) InitDefault() {
}

func (p *IntelligencePermissionInfo) GetInCollaboration() (v bool) {
	return p.InCollaboration
}

func (p *IntelligencePermissionInfo) GetCanDelete() (v bool) {
	return p.CanDelete
}

func (p *IntelligencePermissionInfo) GetCanView() (v bool) {
	return p.CanView
}

var fieldIDToName_IntelligencePermissionInfo = map[int16]string{
	1: "in_collaboration",
	2: "can_delete",
	3: "can_view",
}

func (p *IntelligencePermissionInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IntelligencePermissionInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *IntelligencePermissionInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InCollaboration = _field
	return nil
}
func (p *IntelligencePermissionInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanDelete = _field
	return nil
}
func (p *IntelligencePermissionInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanView = _field
	return nil
}

func (p *IntelligencePermissionInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("IntelligencePermissionInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IntelligencePermissionInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("in_collaboration", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.InCollaboration); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *IntelligencePermissionInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_delete", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanDelete); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *IntelligencePermissionInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_view", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanView); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *IntelligencePermissionInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntelligencePermissionInfo(%+v)", *p)

}

type FavoriteInfo struct {
	// 是否收藏；收藏列表使用
	IsFav bool `thrift:"is_fav,1" form:"is_fav" json:"is_fav" query:"is_fav"`
	// 收藏时间；收藏列表使用
	FavTime string `thrift:"fav_time,2" form:"fav_time" json:"fav_time" query:"fav_time"`
}

func NewFavoriteInfo() *FavoriteInfo {
	return &FavoriteInfo{}
}

func (p *FavoriteInfo) InitDefault() {
}

func (p *FavoriteInfo) GetIsFav() (v bool) {
	return p.IsFav
}

func (p *FavoriteInfo) GetFavTime() (v string) {
	return p.FavTime
}

var fieldIDToName_FavoriteInfo = map[int16]string{
	1: "is_fav",
	2: "fav_time",
}

func (p *FavoriteInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FavoriteInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FavoriteInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsFav = _field
	return nil
}
func (p *FavoriteInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FavTime = _field
	return nil
}

func (p *FavoriteInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FavoriteInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FavoriteInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_fav", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsFav); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FavoriteInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fav_time", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FavTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *FavoriteInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FavoriteInfo(%+v)", *p)

}

type OtherInfo struct {
	// 最近打开时间；最近打开筛选时使用
	RecentlyOpenTime string `thrift:"recently_open_time,1" form:"recently_open_time" json:"recently_open_time" query:"recently_open_time"`
	// 仅bot类型返回
	BotMode BotMode `thrift:"bot_mode,2" form:"bot_mode" json:"bot_mode" query:"bot_mode"`
}

func NewOtherInfo() *OtherInfo {
	return &OtherInfo{}
}

func (p *OtherInfo) InitDefault() {
}

func (p *OtherInfo) GetRecentlyOpenTime() (v string) {
	return p.RecentlyOpenTime
}

func (p *OtherInfo) GetBotMode() (v BotMode) {
	return p.BotMode
}

var fieldIDToName_OtherInfo = map[int16]string{
	1: "recently_open_time",
	2: "bot_mode",
}

func (p *OtherInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OtherInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OtherInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecentlyOpenTime = _field
	return nil
}
func (p *OtherInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field BotMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BotMode(v)
	}
	p.BotMode = _field
	return nil
}

func (p *OtherInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("OtherInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OtherInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("recently_open_time", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecentlyOpenTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OtherInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_mode", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BotMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *OtherInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OtherInfo(%+v)", *p)

}

type Intelligence struct {
	// 基本信息
	BasicInfo *common.IntelligenceBasicInfo `thrift:"basic_info,1" form:"basic_info" json:"basic_info" query:"basic_info"`
	// 智能体类型
	Type common.IntelligenceType `thrift:"type,2" form:"type" json:"type" query:"type"`
	// 智能体发布信息，可选
	PublishInfo *IntelligencePublishInfo `thrift:"publish_info,3" form:"publish_info" json:"publish_info" query:"publish_info"`
	// 智能体所有者信息，可选
	OwnerInfo *common.User `thrift:"owner_info,4" form:"owner_info" json:"owner_info" query:"owner_info"`
	// 当前用户对智能体的权限信息，可选
	PermissionInfo *IntelligencePermissionInfo `thrift:"permission_info,5" form:"permission_info" json:"permission_info" query:"permission_info"`
}

func NewIntelligence() *Intelligence {
	return &Intelligence{}
}

func (p *Intelligence) InitDefault() {
}

var Intelligence_BasicInfo_DEFAULT *common.IntelligenceBasicInfo

func (p *Intelligence) GetBasicInfo() (v *common.IntelligenceBasicInfo) {
	if !p.IsSetBasicInfo() {
		return Intelligence_BasicInfo_DEFAULT
	}
	return p.BasicInfo
}

func (p *Intelligence) GetType() (v common.IntelligenceType) {
	return p.Type
}

var Intelligence_PublishInfo_DEFAULT *IntelligencePublishInfo

func (p *Intelligence) GetPublishInfo() (v *IntelligencePublishInfo) {
	if !p.IsSetPublishInfo() {
		return Intelligence_PublishInfo_DEFAULT
	}
	return p.PublishInfo
}

var Intelligence_OwnerInfo_DEFAULT *common.User

func (p *Intelligence) GetOwnerInfo() (v *common.User) {
	if !p.IsSetOwnerInfo() {
		return Intelligence_OwnerInfo_DEFAULT
	}
	return p.OwnerInfo
}

var Intelligence_PermissionInfo_DEFAULT *IntelligencePermissionInfo

func (p *Intelligence) GetPermissionInfo() (v *IntelligencePermissionInfo) {
	if !p.IsSetPermissionInfo() {
		return Intelligence_PermissionInfo_DEFAULT
	}
	return p.PermissionInfo
}

var fieldIDToName_Intelligence = map[int16]string{
	1: "basic_info",
	2: "type",
	3: "publish_info",
	4: "owner_info",
	5: "permission_info",
}

func (p *Intelligence) IsSetBasicInfo() bool {
	return p.BasicInfo != nil
}

func (p *Intelligence) IsSetPublishInfo() bool {
	return p.PublishInfo != nil
}

func (p *Intelligence) IsSetOwnerInfo() bool {
	return p.OwnerInfo != nil
}

func (p *Intelligence) IsSetPermissionInfo() bool {
	return p.PermissionInfo != nil
}

func (p *Intelligence) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Intelligence[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Intelligence) ReadField1(iprot thrift.TProtocol) error {
	_field := common.NewIntelligenceBasicInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BasicInfo = _field
	return nil
}
func (p *Intelligence) ReadField2(iprot thrift.TProtocol) error {

	var _field common.IntelligenceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = common.IntelligenceType(v)
	}
	p.Type = _field
	return nil
}
func (p *Intelligence) ReadField3(iprot thrift.TProtocol) error {
	_field := NewIntelligencePublishInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PublishInfo = _field
	return nil
}
func (p *Intelligence) ReadField4(iprot thrift.TProtocol) error {
	_field := common.NewUser()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OwnerInfo = _field
	return nil
}
func (p *Intelligence) ReadField5(iprot thrift.TProtocol) error {
	_field := NewIntelligencePermissionInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PermissionInfo = _field
	return nil
}

func (p *Intelligence) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Intelligence"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Intelligence) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("basic_info", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BasicInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Intelligence) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Intelligence) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_info", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PublishInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Intelligence) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("owner_info", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.OwnerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Intelligence) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("permission_info", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PermissionInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Intelligence) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Intelligence(%+v)", *p)

}

// For前端
type IntelligenceData struct {
	BasicInfo       *common.IntelligenceBasicInfo `thrift:"basic_info,1" form:"basic_info" json:"basic_info" query:"basic_info"`
	Type            common.IntelligenceType       `thrift:"type,2" form:"type" json:"type" query:"type"`
	PublishInfo     *IntelligencePublishInfo      `thrift:"publish_info,3" form:"publish_info" json:"publish_info" query:"publish_info"`
	PermissionInfo  *IntelligencePermissionInfo   `thrift:"permission_info,4" form:"permission_info" json:"permission_info" query:"permission_info"`
	OwnerInfo       *common.User                  `thrift:"owner_info,5" form:"owner_info" json:"owner_info" query:"owner_info"`
	LatestAuditInfo *common.AuditInfo             `thrift:"latest_audit_info,6" form:"latest_audit_info" json:"latest_audit_info" query:"latest_audit_info"`
	FavoriteInfo    *FavoriteInfo                 `thrift:"favorite_info,7" form:"favorite_info" json:"favorite_info" query:"favorite_info"`
	OtherInfo       *OtherInfo                    `thrift:"other_info,50" form:"other_info" json:"other_info" query:"other_info"`
}

func NewIntelligenceData() *IntelligenceData {
	return &IntelligenceData{}
}

func (p *IntelligenceData) InitDefault() {
}

var IntelligenceData_BasicInfo_DEFAULT *common.IntelligenceBasicInfo

func (p *IntelligenceData) GetBasicInfo() (v *common.IntelligenceBasicInfo) {
	if !p.IsSetBasicInfo() {
		return IntelligenceData_BasicInfo_DEFAULT
	}
	return p.BasicInfo
}

func (p *IntelligenceData) GetType() (v common.IntelligenceType) {
	return p.Type
}

var IntelligenceData_PublishInfo_DEFAULT *IntelligencePublishInfo

func (p *IntelligenceData) GetPublishInfo() (v *IntelligencePublishInfo) {
	if !p.IsSetPublishInfo() {
		return IntelligenceData_PublishInfo_DEFAULT
	}
	return p.PublishInfo
}

var IntelligenceData_PermissionInfo_DEFAULT *IntelligencePermissionInfo

func (p *IntelligenceData) GetPermissionInfo() (v *IntelligencePermissionInfo) {
	if !p.IsSetPermissionInfo() {
		return IntelligenceData_PermissionInfo_DEFAULT
	}
	return p.PermissionInfo
}

var IntelligenceData_OwnerInfo_DEFAULT *common.User

func (p *IntelligenceData) GetOwnerInfo() (v *common.User) {
	if !p.IsSetOwnerInfo() {
		return IntelligenceData_OwnerInfo_DEFAULT
	}
	return p.OwnerInfo
}

var IntelligenceData_LatestAuditInfo_DEFAULT *common.AuditInfo

func (p *IntelligenceData) GetLatestAuditInfo() (v *common.AuditInfo) {
	if !p.IsSetLatestAuditInfo() {
		return IntelligenceData_LatestAuditInfo_DEFAULT
	}
	return p.LatestAuditInfo
}

var IntelligenceData_FavoriteInfo_DEFAULT *FavoriteInfo

func (p *IntelligenceData) GetFavoriteInfo() (v *FavoriteInfo) {
	if !p.IsSetFavoriteInfo() {
		return IntelligenceData_FavoriteInfo_DEFAULT
	}
	return p.FavoriteInfo
}

var IntelligenceData_OtherInfo_DEFAULT *OtherInfo

func (p *IntelligenceData) GetOtherInfo() (v *OtherInfo) {
	if !p.IsSetOtherInfo() {
		return IntelligenceData_OtherInfo_DEFAULT
	}
	return p.OtherInfo
}

var fieldIDToName_IntelligenceData = map[int16]string{
	1:  "basic_info",
	2:  "type",
	3:  "publish_info",
	4:  "permission_info",
	5:  "owner_info",
	6:  "latest_audit_info",
	7:  "favorite_info",
	50: "other_info",
}

func (p *IntelligenceData) IsSetBasicInfo() bool {
	return p.BasicInfo != nil
}

func (p *IntelligenceData) IsSetPublishInfo() bool {
	return p.PublishInfo != nil
}

func (p *IntelligenceData) IsSetPermissionInfo() bool {
	return p.PermissionInfo != nil
}

func (p *IntelligenceData) IsSetOwnerInfo() bool {
	return p.OwnerInfo != nil
}

func (p *IntelligenceData) IsSetLatestAuditInfo() bool {
	return p.LatestAuditInfo != nil
}

func (p *IntelligenceData) IsSetFavoriteInfo() bool {
	return p.FavoriteInfo != nil
}

func (p *IntelligenceData) IsSetOtherInfo() bool {
	return p.OtherInfo != nil
}

func (p *IntelligenceData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 50:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField50(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IntelligenceData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *IntelligenceData) ReadField1(iprot thrift.TProtocol) error {
	_field := common.NewIntelligenceBasicInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BasicInfo = _field
	return nil
}
func (p *IntelligenceData) ReadField2(iprot thrift.TProtocol) error {

	var _field common.IntelligenceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = common.IntelligenceType(v)
	}
	p.Type = _field
	return nil
}
func (p *IntelligenceData) ReadField3(iprot thrift.TProtocol) error {
	_field := NewIntelligencePublishInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PublishInfo = _field
	return nil
}
func (p *IntelligenceData) ReadField4(iprot thrift.TProtocol) error {
	_field := NewIntelligencePermissionInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PermissionInfo = _field
	return nil
}
func (p *IntelligenceData) ReadField5(iprot thrift.TProtocol) error {
	_field := common.NewUser()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OwnerInfo = _field
	return nil
}
func (p *IntelligenceData) ReadField6(iprot thrift.TProtocol) error {
	_field := common.NewAuditInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LatestAuditInfo = _field
	return nil
}
func (p *IntelligenceData) ReadField7(iprot thrift.TProtocol) error {
	_field := NewFavoriteInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FavoriteInfo = _field
	return nil
}
func (p *IntelligenceData) ReadField50(iprot thrift.TProtocol) error {
	_field := NewOtherInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OtherInfo = _field
	return nil
}

func (p *IntelligenceData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("IntelligenceData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField50(oprot); err != nil {
			fieldId = 50
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IntelligenceData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("basic_info", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BasicInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *IntelligenceData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *IntelligenceData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_info", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PublishInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *IntelligenceData) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("permission_info", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PermissionInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *IntelligenceData) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("owner_info", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.OwnerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *IntelligenceData) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("latest_audit_info", thrift.STRUCT, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.LatestAuditInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *IntelligenceData) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("favorite_info", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.FavoriteInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *IntelligenceData) writeField50(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("other_info", thrift.STRUCT, 50); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.OtherInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 end error: ", p), err)
}

func (p *IntelligenceData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntelligenceData(%+v)", *p)

}

type DraftIntelligenceListData struct {
	Intelligences []*IntelligenceData `thrift:"intelligences,1" form:"intelligences" json:"intelligences" query:"intelligences"`
	Total         int32               `thrift:"total,2" form:"total" json:"total" query:"total"`
	HasMore       bool                `thrift:"has_more,3" form:"has_more" json:"has_more" query:"has_more"`
	NextCursorID  string              `thrift:"next_cursor_id,4" form:"next_cursor_id" json:"next_cursor_id" query:"next_cursor_id"`
}

func NewDraftIntelligenceListData() *DraftIntelligenceListData {
	return &DraftIntelligenceListData{}
}

func (p *DraftIntelligenceListData) InitDefault() {
}

func (p *DraftIntelligenceListData) GetIntelligences() (v []*IntelligenceData) {
	return p.Intelligences
}

func (p *DraftIntelligenceListData) GetTotal() (v int32) {
	return p.Total
}

func (p *DraftIntelligenceListData) GetHasMore() (v bool) {
	return p.HasMore
}

func (p *DraftIntelligenceListData) GetNextCursorID() (v string) {
	return p.NextCursorID
}

var fieldIDToName_DraftIntelligenceListData = map[int16]string{
	1: "intelligences",
	2: "total",
	3: "has_more",
	4: "next_cursor_id",
}

func (p *DraftIntelligenceListData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DraftIntelligenceListData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DraftIntelligenceListData) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*IntelligenceData, 0, size)
	values := make([]IntelligenceData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Intelligences = _field
	return nil
}
func (p *DraftIntelligenceListData) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DraftIntelligenceListData) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasMore = _field
	return nil
}
func (p *DraftIntelligenceListData) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NextCursorID = _field
	return nil
}

func (p *DraftIntelligenceListData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DraftIntelligenceListData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DraftIntelligenceListData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("intelligences", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Intelligences)); err != nil {
		return err
	}
	for _, v := range p.Intelligences {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DraftIntelligenceListData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DraftIntelligenceListData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("has_more", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasMore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DraftIntelligenceListData) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("next_cursor_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NextCursorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DraftIntelligenceListData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DraftIntelligenceListData(%+v)", *p)

}

type GetDraftIntelligenceListResponse struct {
	Data     *DraftIntelligenceListData `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int32                      `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string                     `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp             `thrift:"BaseResp,255,optional" form:"-" json:"-" query:"-"`
}

func NewGetDraftIntelligenceListResponse() *GetDraftIntelligenceListResponse {
	return &GetDraftIntelligenceListResponse{}
}

func (p *GetDraftIntelligenceListResponse) InitDefault() {
}

var GetDraftIntelligenceListResponse_Data_DEFAULT *DraftIntelligenceListData

func (p *GetDraftIntelligenceListResponse) GetData() (v *DraftIntelligenceListData) {
	if !p.IsSetData() {
		return GetDraftIntelligenceListResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *GetDraftIntelligenceListResponse) GetCode() (v int32) {
	return p.Code
}

func (p *GetDraftIntelligenceListResponse) GetMsg() (v string) {
	return p.Msg
}

var GetDraftIntelligenceListResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetDraftIntelligenceListResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetDraftIntelligenceListResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetDraftIntelligenceListResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetDraftIntelligenceListResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetDraftIntelligenceListResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetDraftIntelligenceListResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDraftIntelligenceListResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetDraftIntelligenceListResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDraftIntelligenceListData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetDraftIntelligenceListResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetDraftIntelligenceListResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetDraftIntelligenceListResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetDraftIntelligenceListResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDraftIntelligenceListResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDraftIntelligenceListResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetDraftIntelligenceListResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetDraftIntelligenceListResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetDraftIntelligenceListResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetDraftIntelligenceListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDraftIntelligenceListResponse(%+v)", *p)

}

type GetDraftIntelligenceInfoRequest struct {
	IntelligenceID   int64                   `thrift:"intelligence_id,1" form:"intelligence_id" json:"intelligence_id,string" query:"intelligence_id"`
	IntelligenceType common.IntelligenceType `thrift:"intelligence_type,2" form:"intelligence_type" json:"intelligence_type" query:"intelligence_type"`
	// 预览版本时传入
	Version *int64     `thrift:"version,3,optional" form:"version" json:"version,string,omitempty" query:"version"`
	Base    *base.Base `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetDraftIntelligenceInfoRequest() *GetDraftIntelligenceInfoRequest {
	return &GetDraftIntelligenceInfoRequest{}
}

func (p *GetDraftIntelligenceInfoRequest) InitDefault() {
}

func (p *GetDraftIntelligenceInfoRequest) GetIntelligenceID() (v int64) {
	return p.IntelligenceID
}

func (p *GetDraftIntelligenceInfoRequest) GetIntelligenceType() (v common.IntelligenceType) {
	return p.IntelligenceType
}

var GetDraftIntelligenceInfoRequest_Version_DEFAULT int64

func (p *GetDraftIntelligenceInfoRequest) GetVersion() (v int64) {
	if !p.IsSetVersion() {
		return GetDraftIntelligenceInfoRequest_Version_DEFAULT
	}
	return *p.Version
}

var GetDraftIntelligenceInfoRequest_Base_DEFAULT *base.Base

func (p *GetDraftIntelligenceInfoRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetDraftIntelligenceInfoRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetDraftIntelligenceInfoRequest = map[int16]string{
	1:   "intelligence_id",
	2:   "intelligence_type",
	3:   "version",
	255: "Base",
}

func (p *GetDraftIntelligenceInfoRequest) IsSetVersion() bool {
	return p.Version != nil
}

func (p *GetDraftIntelligenceInfoRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetDraftIntelligenceInfoRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDraftIntelligenceInfoRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IntelligenceID = _field
	return nil
}
func (p *GetDraftIntelligenceInfoRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field common.IntelligenceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = common.IntelligenceType(v)
	}
	p.IntelligenceType = _field
	return nil
}
func (p *GetDraftIntelligenceInfoRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Version = _field
	return nil
}
func (p *GetDraftIntelligenceInfoRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetDraftIntelligenceInfoRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDraftIntelligenceInfoRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("intelligence_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.IntelligenceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("intelligence_type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.IntelligenceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersion() {
		if err = oprot.WriteFieldBegin("version", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Version); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDraftIntelligenceInfoRequest(%+v)", *p)

}

type GetDraftIntelligenceInfoData struct {
	IntelligenceType common.IntelligenceType       `thrift:"intelligence_type,1" form:"intelligence_type" json:"intelligence_type" query:"intelligence_type"`
	BasicInfo        *common.IntelligenceBasicInfo `thrift:"basic_info,2" form:"basic_info" json:"basic_info" query:"basic_info"`
	PublishInfo      *IntelligencePublishInfo      `thrift:"publish_info,3,optional" form:"publish_info" json:"publish_info,omitempty" query:"publish_info"`
	OwnerInfo        *common.User                  `thrift:"owner_info,4,optional" form:"owner_info" json:"owner_info,omitempty" query:"owner_info"`
}

func NewGetDraftIntelligenceInfoData() *GetDraftIntelligenceInfoData {
	return &GetDraftIntelligenceInfoData{}
}

func (p *GetDraftIntelligenceInfoData) InitDefault() {
}

func (p *GetDraftIntelligenceInfoData) GetIntelligenceType() (v common.IntelligenceType) {
	return p.IntelligenceType
}

var GetDraftIntelligenceInfoData_BasicInfo_DEFAULT *common.IntelligenceBasicInfo

func (p *GetDraftIntelligenceInfoData) GetBasicInfo() (v *common.IntelligenceBasicInfo) {
	if !p.IsSetBasicInfo() {
		return GetDraftIntelligenceInfoData_BasicInfo_DEFAULT
	}
	return p.BasicInfo
}

var GetDraftIntelligenceInfoData_PublishInfo_DEFAULT *IntelligencePublishInfo

func (p *GetDraftIntelligenceInfoData) GetPublishInfo() (v *IntelligencePublishInfo) {
	if !p.IsSetPublishInfo() {
		return GetDraftIntelligenceInfoData_PublishInfo_DEFAULT
	}
	return p.PublishInfo
}

var GetDraftIntelligenceInfoData_OwnerInfo_DEFAULT *common.User

func (p *GetDraftIntelligenceInfoData) GetOwnerInfo() (v *common.User) {
	if !p.IsSetOwnerInfo() {
		return GetDraftIntelligenceInfoData_OwnerInfo_DEFAULT
	}
	return p.OwnerInfo
}

var fieldIDToName_GetDraftIntelligenceInfoData = map[int16]string{
	1: "intelligence_type",
	2: "basic_info",
	3: "publish_info",
	4: "owner_info",
}

func (p *GetDraftIntelligenceInfoData) IsSetBasicInfo() bool {
	return p.BasicInfo != nil
}

func (p *GetDraftIntelligenceInfoData) IsSetPublishInfo() bool {
	return p.PublishInfo != nil
}

func (p *GetDraftIntelligenceInfoData) IsSetOwnerInfo() bool {
	return p.OwnerInfo != nil
}

func (p *GetDraftIntelligenceInfoData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDraftIntelligenceInfoData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoData) ReadField1(iprot thrift.TProtocol) error {

	var _field common.IntelligenceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = common.IntelligenceType(v)
	}
	p.IntelligenceType = _field
	return nil
}
func (p *GetDraftIntelligenceInfoData) ReadField2(iprot thrift.TProtocol) error {
	_field := common.NewIntelligenceBasicInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BasicInfo = _field
	return nil
}
func (p *GetDraftIntelligenceInfoData) ReadField3(iprot thrift.TProtocol) error {
	_field := NewIntelligencePublishInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PublishInfo = _field
	return nil
}
func (p *GetDraftIntelligenceInfoData) ReadField4(iprot thrift.TProtocol) error {
	_field := common.NewUser()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OwnerInfo = _field
	return nil
}

func (p *GetDraftIntelligenceInfoData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDraftIntelligenceInfoData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("intelligence_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.IntelligenceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("basic_info", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BasicInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoData) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishInfo() {
		if err = oprot.WriteFieldBegin("publish_info", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.PublishInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoData) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOwnerInfo() {
		if err = oprot.WriteFieldBegin("owner_info", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.OwnerInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDraftIntelligenceInfoData(%+v)", *p)

}

type GetDraftIntelligenceInfoResponse struct {
	Data     *GetDraftIntelligenceInfoData `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int32                         `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string                        `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp                `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewGetDraftIntelligenceInfoResponse() *GetDraftIntelligenceInfoResponse {
	return &GetDraftIntelligenceInfoResponse{}
}

func (p *GetDraftIntelligenceInfoResponse) InitDefault() {
}

var GetDraftIntelligenceInfoResponse_Data_DEFAULT *GetDraftIntelligenceInfoData

func (p *GetDraftIntelligenceInfoResponse) GetData() (v *GetDraftIntelligenceInfoData) {
	if !p.IsSetData() {
		return GetDraftIntelligenceInfoResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *GetDraftIntelligenceInfoResponse) GetCode() (v int32) {
	return p.Code
}

func (p *GetDraftIntelligenceInfoResponse) GetMsg() (v string) {
	return p.Msg
}

var GetDraftIntelligenceInfoResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetDraftIntelligenceInfoResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetDraftIntelligenceInfoResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetDraftIntelligenceInfoResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetDraftIntelligenceInfoResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetDraftIntelligenceInfoResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetDraftIntelligenceInfoResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetDraftIntelligenceInfoResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetDraftIntelligenceInfoData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetDraftIntelligenceInfoResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetDraftIntelligenceInfoResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetDraftIntelligenceInfoResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetDraftIntelligenceInfoResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDraftIntelligenceInfoResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetDraftIntelligenceInfoResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetDraftIntelligenceInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDraftIntelligenceInfoResponse(%+v)", *p)

}

type GetUserRecentlyEditIntelligenceRequest struct {
	Size  int32                     `thrift:"size,1" form:"size" json:"size" query:"size"`
	Types []common.IntelligenceType `thrift:"types,2,optional" form:"types" json:"types,omitempty" query:"types"`
	// 企业id
	EnterpriseID *string `thrift:"enterprise_id,3,optional" form:"enterprise_id" json:"enterprise_id,omitempty" query:"enterprise_id"`
	// 组织id
	OrganizationID *string    `thrift:"organization_id,4,optional" form:"organization_id" json:"organization_id,omitempty" query:"organization_id"`
	Base           *base.Base `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetUserRecentlyEditIntelligenceRequest() *GetUserRecentlyEditIntelligenceRequest {
	return &GetUserRecentlyEditIntelligenceRequest{}
}

func (p *GetUserRecentlyEditIntelligenceRequest) InitDefault() {
}

func (p *GetUserRecentlyEditIntelligenceRequest) GetSize() (v int32) {
	return p.Size
}

var GetUserRecentlyEditIntelligenceRequest_Types_DEFAULT []common.IntelligenceType

func (p *GetUserRecentlyEditIntelligenceRequest) GetTypes() (v []common.IntelligenceType) {
	if !p.IsSetTypes() {
		return GetUserRecentlyEditIntelligenceRequest_Types_DEFAULT
	}
	return p.Types
}

var GetUserRecentlyEditIntelligenceRequest_EnterpriseID_DEFAULT string

func (p *GetUserRecentlyEditIntelligenceRequest) GetEnterpriseID() (v string) {
	if !p.IsSetEnterpriseID() {
		return GetUserRecentlyEditIntelligenceRequest_EnterpriseID_DEFAULT
	}
	return *p.EnterpriseID
}

var GetUserRecentlyEditIntelligenceRequest_OrganizationID_DEFAULT string

func (p *GetUserRecentlyEditIntelligenceRequest) GetOrganizationID() (v string) {
	if !p.IsSetOrganizationID() {
		return GetUserRecentlyEditIntelligenceRequest_OrganizationID_DEFAULT
	}
	return *p.OrganizationID
}

var GetUserRecentlyEditIntelligenceRequest_Base_DEFAULT *base.Base

func (p *GetUserRecentlyEditIntelligenceRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetUserRecentlyEditIntelligenceRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetUserRecentlyEditIntelligenceRequest = map[int16]string{
	1:   "size",
	2:   "types",
	3:   "enterprise_id",
	4:   "organization_id",
	255: "Base",
}

func (p *GetUserRecentlyEditIntelligenceRequest) IsSetTypes() bool {
	return p.Types != nil
}

func (p *GetUserRecentlyEditIntelligenceRequest) IsSetEnterpriseID() bool {
	return p.EnterpriseID != nil
}

func (p *GetUserRecentlyEditIntelligenceRequest) IsSetOrganizationID() bool {
	return p.OrganizationID != nil
}

func (p *GetUserRecentlyEditIntelligenceRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetUserRecentlyEditIntelligenceRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetUserRecentlyEditIntelligenceRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Size = _field
	return nil
}
func (p *GetUserRecentlyEditIntelligenceRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]common.IntelligenceType, 0, size)
	for i := 0; i < size; i++ {

		var _elem common.IntelligenceType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = common.IntelligenceType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Types = _field
	return nil
}
func (p *GetUserRecentlyEditIntelligenceRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnterpriseID = _field
	return nil
}
func (p *GetUserRecentlyEditIntelligenceRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrganizationID = _field
	return nil
}
func (p *GetUserRecentlyEditIntelligenceRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetUserRecentlyEditIntelligenceRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetUserRecentlyEditIntelligenceRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("size", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Size); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetUserRecentlyEditIntelligenceRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypes() {
		if err = oprot.WriteFieldBegin("types", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Types)); err != nil {
			return err
		}
		for _, v := range p.Types {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetUserRecentlyEditIntelligenceRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnterpriseID() {
		if err = oprot.WriteFieldBegin("enterprise_id", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EnterpriseID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetUserRecentlyEditIntelligenceRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrganizationID() {
		if err = oprot.WriteFieldBegin("organization_id", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrganizationID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetUserRecentlyEditIntelligenceRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserRecentlyEditIntelligenceRequest(%+v)", *p)

}

type GetUserRecentlyEditIntelligenceData struct {
	IntelligenceInfoList []*IntelligenceData `thrift:"intelligence_info_list,1" form:"intelligence_info_list" json:"intelligence_info_list" query:"intelligence_info_list"`
}

func NewGetUserRecentlyEditIntelligenceData() *GetUserRecentlyEditIntelligenceData {
	return &GetUserRecentlyEditIntelligenceData{}
}

func (p *GetUserRecentlyEditIntelligenceData) InitDefault() {
}

func (p *GetUserRecentlyEditIntelligenceData) GetIntelligenceInfoList() (v []*IntelligenceData) {
	return p.IntelligenceInfoList
}

var fieldIDToName_GetUserRecentlyEditIntelligenceData = map[int16]string{
	1: "intelligence_info_list",
}

func (p *GetUserRecentlyEditIntelligenceData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetUserRecentlyEditIntelligenceData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceData) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*IntelligenceData, 0, size)
	values := make([]IntelligenceData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.IntelligenceInfoList = _field
	return nil
}

func (p *GetUserRecentlyEditIntelligenceData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetUserRecentlyEditIntelligenceData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("intelligence_info_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.IntelligenceInfoList)); err != nil {
		return err
	}
	for _, v := range p.IntelligenceInfoList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserRecentlyEditIntelligenceData(%+v)", *p)

}

type GetUserRecentlyEditIntelligenceResponse struct {
	Data     *GetUserRecentlyEditIntelligenceData `thrift:"data,1" form:"data" json:"data" query:"data"`
	Code     int32                                `thrift:"code,253" form:"code" json:"code" query:"code"`
	Msg      string                               `thrift:"msg,254" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp                       `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewGetUserRecentlyEditIntelligenceResponse() *GetUserRecentlyEditIntelligenceResponse {
	return &GetUserRecentlyEditIntelligenceResponse{}
}

func (p *GetUserRecentlyEditIntelligenceResponse) InitDefault() {
}

var GetUserRecentlyEditIntelligenceResponse_Data_DEFAULT *GetUserRecentlyEditIntelligenceData

func (p *GetUserRecentlyEditIntelligenceResponse) GetData() (v *GetUserRecentlyEditIntelligenceData) {
	if !p.IsSetData() {
		return GetUserRecentlyEditIntelligenceResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *GetUserRecentlyEditIntelligenceResponse) GetCode() (v int32) {
	return p.Code
}

func (p *GetUserRecentlyEditIntelligenceResponse) GetMsg() (v string) {
	return p.Msg
}

var GetUserRecentlyEditIntelligenceResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetUserRecentlyEditIntelligenceResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetUserRecentlyEditIntelligenceResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetUserRecentlyEditIntelligenceResponse = map[int16]string{
	1:   "data",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetUserRecentlyEditIntelligenceResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetUserRecentlyEditIntelligenceResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetUserRecentlyEditIntelligenceResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetUserRecentlyEditIntelligenceResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetUserRecentlyEditIntelligenceData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetUserRecentlyEditIntelligenceResponse) ReadField253(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetUserRecentlyEditIntelligenceResponse) ReadField254(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetUserRecentlyEditIntelligenceResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetUserRecentlyEditIntelligenceResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetUserRecentlyEditIntelligenceResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Data.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetUserRecentlyEditIntelligenceResponse) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetUserRecentlyEditIntelligenceResponse) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetUserRecentlyEditIntelligenceResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetUserRecentlyEditIntelligenceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserRecentlyEditIntelligenceResponse(%+v)", *p)

}
