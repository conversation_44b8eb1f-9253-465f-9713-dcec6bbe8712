info:
  description: 任务拆解
  title: 板栗看板
  version: v1
openapi: 3.0.1
paths:
  /generate_tasks:
    post:
      operationId: generateTasks
      requestBody:
        content:
          application/json:
            schema:
              properties:
                input:
                  description: 用户的完整输入内容，只支持中文
                  type: string
              required:
                - input
              type: object
        description: new desc
      responses:
        "200":
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: number
                  data:
                    properties:
                      edit_text:
                        type: string
                      edit_url:
                        type: string
                      img_url:
                        type: string
                      view_text:
                        type: string
                    type: object
                  type_for_model:
                    type: number
                    x-global-disable: true
                type: object
          description: new desc
        default:
          description: ""
      summary: 根据用户提问拆解任务，生成任务看板
servers:
  - url: https://banlikanban.com/ai/coze
