name: License Check

on:
  push:
    branches: ['main']
  pull_request:

  workflow_dispatch:

jobs:
  license-check:
    name: License Check
    runs-on: ubuntu-latest
    steps:
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Install license-eye
        run: |
          go install github.com/apache/skywalking-eyes/cmd/license-eye@v0.4.0

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: License Header Check
        run: |
          license-eye header check -c .github/.licenserc.yaml

      - name: License Dependency Check
        run: |
          license-eye dependency check -c .github/.licenserc.yaml
